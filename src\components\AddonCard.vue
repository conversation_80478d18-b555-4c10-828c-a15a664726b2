<template>
  <div class="card p-6 hover:shadow-md transition-shadow duration-200 cursor-pointer" @click="$emit('viewDetails', addon.id)">
    <!-- Header -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
          {{ addon.name }}
        </h3>
        <div class="flex items-center space-x-2 mt-1">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {{ formatCategory(addon.category) }}
          </span>
          <span v-if="addon.featured" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            ⭐ Featured
          </span>
        </div>
      </div>
      
      <!-- Status Icon -->
      <div class="ml-4">
        <div v-if="addon.isInstalled" class="w-8 h-8 bg-turtle-100 dark:bg-turtle-900 rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-turtle-600 dark:text-turtle-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div v-else-if="linkTypeIcon" class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <component :is="linkTypeIcon" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
        </div>
      </div>
    </div>

    <!-- Description -->
    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
      {{ addon.description }}
    </p>

    <!-- Tags -->
    <div v-if="addon.tags.length > 0" class="flex flex-wrap gap-1 mb-4">
      <span
        v-for="tag in addon.tags.slice(0, 3)"
        :key="tag"
        class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
      >
        {{ tag }}
      </span>
      <span v-if="addon.tags.length > 3" class="text-xs text-gray-500 dark:text-gray-400">
        +{{ addon.tags.length - 3 }} more
      </span>
    </div>

    <!-- Footer -->
    <div class="flex items-center justify-between">
      <!-- Version/Author -->
      <div class="text-xs text-gray-500 dark:text-gray-400">
        <div v-if="addon.version">v{{ addon.version }}</div>
        <div v-if="addon.author">by {{ addon.author }}</div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <!-- Download Progress -->
        <div v-if="downloadProgress" class="flex items-center space-x-2">
          <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-primary-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${downloadProgress.progress}%` }"
            />
          </div>
          <span class="text-xs text-gray-500">{{ downloadProgress.progress }}%</span>
        </div>

        <!-- Action Buttons -->
        <div v-else class="flex space-x-2">
          <!-- Install/Uninstall Button -->
          <button
            v-if="addon.linkType === 'direct_download'"
            @click.stop="addon.isInstalled ? $emit('uninstall', addon.id) : $emit('install', addon.id)"
            :class="addon.isInstalled ? 'btn-danger' : 'btn-success'"
            class="text-sm px-3 py-1"
          >
            {{ addon.isInstalled ? 'Uninstall' : 'Install' }}
          </button>

          <!-- Visit Link Button -->
          <button
            v-else
            @click.stop="openExternalLink(addon.links[0]?.url)"
            class="btn-secondary text-sm px-3 py-1"
          >
            Visit
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAddonStore } from '../stores/addonStore'
import { Addon, LinkType } from '../types/addon'

interface Props {
  addon: Addon
}

const props = defineProps<Props>()

const emit = defineEmits<{
  install: [addonId: string]
  uninstall: [addonId: string]
  viewDetails: [addonId: string]
}>()

const addonStore = useAddonStore()

const downloadProgress = computed(() => {
  return addonStore.getDownloadProgress(props.addon.id)
})

const linkTypeIcon = computed(() => {
  switch (props.addon.linkType) {
    case LinkType.DIRECT_DOWNLOAD:
      return 'DownloadIcon'
    case LinkType.EXTERNAL_ONLY:
      return 'ExternalLinkIcon'
    default:
      return null
  }
})

function formatCategory(category: string): string {
  return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

function openExternalLink(url?: string) {
  if (url) {
    window.electronAPI.openExternal(url)
  }
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
