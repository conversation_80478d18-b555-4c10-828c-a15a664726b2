/**
 * Pinia store for application settings
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { AppSettings, WoWDirectory } from '../types/addon';
import { ElectronAddonManager } from '../services/electronAddonManager';

export const useSettingsStore = defineStore('settings', () => {
  // State
  const settings = ref<AppSettings>({
    wowDirectory: null,
    autoUpdate: true,
    downloadConcurrency: 3,
    backupBeforeUpdate: true,
    theme: 'auto',
    language: 'en',
    notifications: {
      updates: true,
      downloads: true,
      errors: true
    },
    advanced: {
      validateChecksums: false,
      retryAttempts: 3,
      timeoutSeconds: 30
    }
  });

  const isFirstRun = ref(true);

  // Computed
  const isWoWDirectoryConfigured = computed(() => 
    settings.value.wowDirectory !== null && settings.value.wowDirectory.isValid
  );

  const isDarkMode = computed(() => {
    if (settings.value.theme === 'dark') return true;
    if (settings.value.theme === 'light') return false;
    
    // Auto mode - detect system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  // Actions
  async function loadSettings() {
    try {
      console.log('⚙️ Loading settings...')
      console.log('⚙️ window.electronAPI available:', !!window.electronAPI)

      if (!window.electronAPI) {
        console.warn('⚠️ Electron API not available, using defaults')
        isFirstRun.value = true
        return
      }

      const savedSettings = await window.electronAPI.invoke('get-settings') as Partial<AppSettings>;
      const savedFirstRun = await window.electronAPI.invoke('get-first-run') as boolean;

      console.log('⚙️ Loaded from storage:', { savedSettings, savedFirstRun })

      if (savedSettings) {
        // Merge saved settings with defaults
        settings.value = {
          ...settings.value,
          ...savedSettings,
          notifications: {
            ...settings.value.notifications,
            ...savedSettings.notifications
          },
          advanced: {
            ...settings.value.advanced,
            ...savedSettings.advanced
          }
        };
      }

      isFirstRun.value = savedFirstRun ?? true;

      console.log('✅ Settings loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load settings:', error);
      // Use defaults on error
      isFirstRun.value = true;
    }
  }

  async function saveSettings() {
    try {
      await window.electronAPI.invoke('save-settings', settings.value);
      await window.electronAPI.invoke('save-first-run', false);
      isFirstRun.value = false;

      console.log('✅ Settings saved successfully');
    } catch (error) {
      console.error('❌ Failed to save settings:', error);
    }
  }

  function setWoWDirectory(directory: WoWDirectory | null) {
    settings.value.wowDirectory = directory;
    saveSettings();
  }

  async function selectWoWDirectory(): Promise<boolean> {
    try {
      // Use Electron's dialog to select directory
      const result = await window.electronAPI.selectDirectory();

      if (result.canceled || !result.filePaths.length) {
        return false;
      }

      const selectedPath = result.filePaths[0];
      const validation = await window.electronAPI.validateWoWDirectory(selectedPath);

      if (validation && validation.isValid) {
        setWoWDirectory(validation);
        return true;
      } else {
        throw new Error(validation?.error || 'Selected directory is not a valid WoW installation');
      }
    } catch (error) {
      console.error('Failed to select WoW directory:', error);
      return false;
    }
  }

  function setTheme(theme: 'light' | 'dark' | 'auto') {
    settings.value.theme = theme;
    saveSettings();
    
    // Apply theme immediately
    applyTheme();
  }

  function applyTheme() {
    const isDark = isDarkMode.value;
    
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  function setAutoUpdate(enabled: boolean) {
    settings.value.autoUpdate = enabled;
    saveSettings();
  }

  function setDownloadConcurrency(concurrency: number) {
    settings.value.downloadConcurrency = Math.max(1, Math.min(10, concurrency));
    saveSettings();
  }

  function setBackupBeforeUpdate(enabled: boolean) {
    settings.value.backupBeforeUpdate = enabled;
    saveSettings();
  }

  function setLanguage(language: string) {
    settings.value.language = language;
    saveSettings();
  }

  function setNotificationSettings(notifications: Partial<AppSettings['notifications']>) {
    settings.value.notifications = {
      ...settings.value.notifications,
      ...notifications
    };
    saveSettings();
  }

  function setAdvancedSettings(advanced: Partial<AppSettings['advanced']>) {
    settings.value.advanced = {
      ...settings.value.advanced,
      ...advanced
    };
    saveSettings();
  }

  function resetToDefaults() {
    const defaultSettings: AppSettings = {
      wowDirectory: null,
      autoUpdate: true,
      downloadConcurrency: 3,
      backupBeforeUpdate: true,
      theme: 'auto',
      language: 'en',
      notifications: {
        updates: true,
        downloads: true,
        errors: true
      },
      advanced: {
        validateChecksums: false,
        retryAttempts: 3,
        timeoutSeconds: 30
      }
    };
    
    settings.value = defaultSettings;
    saveSettings();
  }

  function exportSettings(): string {
    return JSON.stringify(settings.value, null, 2);
  }

  function importSettings(settingsJson: string): boolean {
    try {
      const importedSettings = JSON.parse(settingsJson) as AppSettings;
      
      // Validate imported settings
      if (typeof importedSettings !== 'object') {
        throw new Error('Invalid settings format');
      }
      
      settings.value = {
        ...settings.value,
        ...importedSettings,
        notifications: {
          ...settings.value.notifications,
          ...importedSettings.notifications
        },
        advanced: {
          ...settings.value.advanced,
          ...importedSettings.advanced
        }
      };
      
      saveSettings();
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }

  // Initialize theme watcher
  function initializeThemeWatcher() {
    if (settings.value.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', applyTheme);
    }
  }

  return {
    // State
    settings,
    isFirstRun,
    
    // Computed
    isWoWDirectoryConfigured,
    isDarkMode,
    
    // Actions
    loadSettings,
    saveSettings,
    setWoWDirectory,
    selectWoWDirectory,
    setTheme,
    applyTheme,
    setAutoUpdate,
    setDownloadConcurrency,
    setBackupBeforeUpdate,
    setLanguage,
    setNotificationSettings,
    setAdvancedSettings,
    resetToDefaults,
    exportSettings,
    importSettings,
    initializeThemeWatcher
  };
});
