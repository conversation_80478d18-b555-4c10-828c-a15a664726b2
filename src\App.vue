<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from './stores/settingsStore'
import { useAddonStore } from './stores/addonStore'
import Sidebar from './components/Sidebar.vue'
import ErrorNotifications from './components/ErrorNotifications.vue'
import FirstRunSetup from './components/FirstRunSetup.vue'

const settingsStore = useSettingsStore()
const addonStore = useAddonStore()

onMounted(async () => {
  console.log('🚀 App mounted, loading settings...')

  // Simple test to see if JavaScript is running
  try {
    // Load settings
    await settingsStore.loadSettings()
    settingsStore.applyTheme()
    settingsStore.initializeThemeWatcher()

    console.log('⚙️ Settings loaded:', {
      isFirstRun: settingsStore.isFirstRun,
      isWoWDirectoryConfigured: settingsStore.isWoWDirectoryConfigured,
      settings: settingsStore.settings
    })

    // Initialize addon store if not first run
    if (!settingsStore.isFirstRun && settingsStore.isWoWDirectoryConfigured) {
      console.log('🎮 Initializing addon store...')
      await addonStore.initialize(settingsStore.settings)
      console.log('✅ Addon store initialized successfully')
    } else {
      console.log('🆕 First run or WoW directory not configured', {
        isFirstRun: settingsStore.isFirstRun,
        isWoWDirectoryConfigured: settingsStore.isWoWDirectoryConfigured
      })
    }
  } catch (error) {
    console.error('❌ Error during app initialization:', error)
  }
})

onUnmounted(() => {
  addonStore.cleanup()
})
</script>

<template>
  <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
    <!-- First Run Setup Modal -->
    <FirstRunSetup v-if="settingsStore.isFirstRun" />

    <!-- Sidebar -->
    <Sidebar v-if="!settingsStore.isFirstRun" />

    <!-- Main Content -->
    <div v-if="!settingsStore.isFirstRun" class="flex-1 flex flex-col overflow-hidden">
      <!-- Router View -->
      <main class="flex-1 overflow-auto">
        <router-view />
      </main>
    </div>

    <!-- Error Notifications -->
    <ErrorNotifications />
  </div>
</template>
