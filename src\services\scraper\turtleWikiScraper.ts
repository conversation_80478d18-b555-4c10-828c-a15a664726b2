/**
 * Turtle WoW Wiki Scraper
 * Extracts addon information from https://turtle-wow.fandom.com/wiki/Addons
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import { ScrapedAddonData, Addon, AddonCategory, LinkType } from '../../types/addon';
import { LinkAnalyzer } from '../linkAnalyzer/linkAnalyzer';

export class TurtleWikiScraper {
  private static readonly WIKI_URL = 'https://turtle-wow.fandom.com/wiki/Addons';
  private static readonly USER_AGENT = 'TurtleWoW-AddonManager/1.0.0 (https://github.com/user/turtle-addon-manager)';
  
  private linkAnalyzer: LinkAnalyzer;

  constructor() {
    this.linkAnalyzer = new LinkAnalyzer();
  }

  /**
   * Scrape all addons from the Turtle WoW wiki
   */
  async scrapeAllAddons(): Promise<Addon[]> {
    try {
      console.log('🕷️ Starting Turtle WoW wiki scrape...');
      
      const response = await axios.get(TurtleWikiScraper.WIKI_URL, {
        headers: {
          'User-Agent': TurtleWikiScraper.USER_AGENT,
        },
        timeout: 30000,
      });

      const $ = cheerio.load(response.data);
      const scrapedData = this.extractAddonData($);
      
      console.log(`📦 Found ${scrapedData.length} addons to process`);
      
      // Convert scraped data to Addon objects
      const addons: Addon[] = [];
      for (const data of scrapedData) {
        try {
          const addon = await this.convertToAddon(data);
          if (addon) {
            addons.push(addon);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to process addon: ${data.name}`, error);
        }
      }

      console.log(`✅ Successfully processed ${addons.length} addons`);
      return addons;
      
    } catch (error) {
      console.error('❌ Failed to scrape Turtle WoW wiki:', error);
      throw new Error(`Wiki scraping failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract addon data from the wiki HTML
   */
  private extractAddonData($: cheerio.CheerioAPI): ScrapedAddonData[] {
    const addons: ScrapedAddonData[] = [];
    
    // Find all alphabetical sections (A, B, C, etc.)
    const sections = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
    
    for (const section of sections) {
      const sectionHeader = $(`h3:contains("${section}")`).first();
      if (sectionHeader.length === 0) continue;

      // Find the list following this section header
      let currentElement = sectionHeader.next();
      
      while (currentElement.length > 0 && !currentElement.is('h3')) {
        if (currentElement.is('ul')) {
          // Process list items
          currentElement.find('li').each((_, element) => {
            const addonData = this.extractAddonFromListItem($, $(element), section);
            if (addonData) {
              addons.push(addonData);
            }
          });
        }
        currentElement = currentElement.next();
      }
    }

    // Also check Featured Addons section
    const featuredSection = $('#Featured_Addons').parent().next();
    if (featuredSection.is('ul')) {
      featuredSection.find('li').each((_, element) => {
        const addonData = this.extractAddonFromListItem($, $(element), 'Featured');
        if (addonData) {
          addons.push(addonData);
        }
      });
    }

    return addons;
  }

  /**
   * Extract addon information from a list item
   */
  private extractAddonFromListItem($: cheerio.CheerioAPI, $item: cheerio.Cheerio, section: string): ScrapedAddonData | null {
    try {
      const text = $item.text().trim();
      if (!text) return null;

      // Find the first link in the item
      const link = $item.find('a').first();
      const url = link.attr('href') || '';
      
      // Extract addon name (usually the link text or text before the first dash/description)
      let name = link.text().trim();
      if (!name) {
        // Fallback: extract name from the beginning of the text
        const match = text.match(/^([^-–]+)/);
        name = match ? match[1].trim() : text.split(' ')[0];
      }

      // Extract description (text after the name/link)
      let description = '';
      const fullText = $item.text();
      
      // Try to find description after a dash or after the link text
      if (name) {
        const nameIndex = fullText.indexOf(name);
        if (nameIndex !== -1) {
          const afterName = fullText.substring(nameIndex + name.length).trim();
          // Remove leading dashes, colons, etc.
          description = afterName.replace(/^[-–:]+\s*/, '').trim();
        }
      }

      // If no description found, use the full text minus the name
      if (!description) {
        description = fullText.replace(name, '').replace(/^[-–:]+\s*/, '').trim();
      }

      // Clean up the URL (handle relative URLs)
      let cleanUrl = url;
      if (url.startsWith('/')) {
        cleanUrl = 'https://turtle-wow.fandom.com' + url;
      } else if (url.startsWith('http')) {
        cleanUrl = url;
      }

      return {
        name: name || 'Unknown',
        description: description || 'No description available',
        url: cleanUrl,
        section,
        rawHtml: $item.html() || '',
        scrapedAt: new Date()
      };
      
    } catch (error) {
      console.warn('Failed to extract addon from list item:', error);
      return null;
    }
  }

  /**
   * Convert scraped data to Addon object
   */
  private async convertToAddon(data: ScrapedAddonData): Promise<Addon | null> {
    try {
      // Analyze the link to determine type and get additional info
      const linkAnalysis = await this.linkAnalyzer.analyzeLink(data.url);
      
      // Determine category based on section and description
      const category = this.determineCategory(data.section, data.description);
      
      // Generate unique ID
      const id = this.generateAddonId(data.name, data.url);

      const addon: Addon = {
        id,
        name: data.name,
        description: data.description,
        category,
        links: [{
          url: data.url,
          type: linkAnalysis.sourceType,
          isPrimary: true,
          label: 'Primary'
        }],
        linkType: linkAnalysis.linkType,
        isInstalled: false,
        tags: this.extractTags(data.description),
        featured: data.section === 'Featured',
        version: linkAnalysis.version,
        author: linkAnalysis.author,
        lastUpdated: linkAnalysis.lastUpdated
      };

      return addon;
      
    } catch (error) {
      console.warn(`Failed to convert scraped data to addon: ${data.name}`, error);
      return null;
    }
  }

  /**
   * Determine addon category based on section and description
   */
  private determineCategory(section: string, description: string): AddonCategory {
    if (section === 'Featured') {
      return AddonCategory.FEATURED;
    }

    const desc = description.toLowerCase();
    
    // UI-related keywords
    if (desc.includes('ui') || desc.includes('interface') || desc.includes('frame') || desc.includes('bar')) {
      return AddonCategory.UI_REPLACEMENT;
    }
    
    // Combat-related keywords
    if (desc.includes('dps') || desc.includes('damage') || desc.includes('threat') || desc.includes('combat')) {
      return AddonCategory.COMBAT;
    }
    
    // Leveling/questing keywords
    if (desc.includes('quest') || desc.includes('leveling') || desc.includes('experience')) {
      return AddonCategory.LEVELING;
    }
    
    // Endgame keywords
    if (desc.includes('raid') || desc.includes('dungeon') || desc.includes('boss') || desc.includes('loot')) {
      return AddonCategory.ENDGAME;
    }
    
    // PvP keywords
    if (desc.includes('pvp') || desc.includes('battleground') || desc.includes('arena')) {
      return AddonCategory.PVP;
    }
    
    // Profession keywords
    if (desc.includes('profession') || desc.includes('craft') || desc.includes('trade')) {
      return AddonCategory.PROFESSION;
    }
    
    // Roleplay keywords
    if (desc.includes('rp') || desc.includes('roleplay') || desc.includes('role-play')) {
      return AddonCategory.ROLEPLAY;
    }
    
    // Library keywords
    if (desc.includes('lib') || desc.includes('library') || desc.includes('framework')) {
      return AddonCategory.LIBRARY;
    }

    return AddonCategory.UTILITY;
  }

  /**
   * Extract tags from description
   */
  private extractTags(description: string): string[] {
    const tags: string[] = [];
    const desc = description.toLowerCase();
    
    // Common addon types
    const tagKeywords = [
      'ui', 'interface', 'dps', 'threat', 'quest', 'map', 'minimap',
      'raid', 'dungeon', 'pvp', 'battleground', 'auction', 'bag',
      'inventory', 'chat', 'guild', 'party', 'target', 'cast',
      'buff', 'debuff', 'cooldown', 'timer', 'bar', 'frame'
    ];
    
    for (const keyword of tagKeywords) {
      if (desc.includes(keyword)) {
        tags.push(keyword);
      }
    }
    
    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Generate unique addon ID
   */
  private generateAddonId(name: string, url: string): string {
    // Create a simple hash-like ID from name and URL
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    const urlHash = url.split('').reduce((hash, char) => {
      return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;
    }, 0);
    
    return `${cleanName}_${Math.abs(urlHash).toString(36)}`;
  }
}
