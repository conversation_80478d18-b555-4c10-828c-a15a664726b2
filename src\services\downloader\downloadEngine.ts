/**
 * Download Engine
 * Handles downloading, extracting, and preparing addon files
 */

import axios, { AxiosProgressEvent } from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as yauzl from 'yauzl';
import { EventEmitter } from 'events';
import { DownloadProgress, DownloadStatus, Addon } from '../../types/addon';

export interface DownloadOptions {
  maxRetries?: number;
  timeoutMs?: number;
  concurrency?: number;
  validateChecksum?: boolean;
}

export class DownloadEngine extends EventEmitter {
  private activeDownloads = new Map<string, AbortController>();
  private downloadQueue: string[] = [];
  private readonly tempDir: string;
  private readonly options: Required<DownloadOptions>;

  constructor(tempDir: string, options: DownloadOptions = {}) {
    super();
    this.tempDir = tempDir;
    this.options = {
      maxRetries: options.maxRetries ?? 3,
      timeoutMs: options.timeoutMs ?? 30000,
      concurrency: options.concurrency ?? 3,
      validateChecksum: options.validateChecksum ?? false
    };

    // Ensure temp directory exists
    this.ensureDirectoryExists(this.tempDir);
  }

  /**
   * Download an addon
   */
  async downloadAddon(addon: Addon): Promise<string> {
    const downloadUrl = this.getDownloadUrl(addon);
    if (!downloadUrl) {
      throw new Error(`No download URL available for addon: ${addon.name}`);
    }

    const progress: DownloadProgress = {
      addonId: addon.id,
      status: DownloadStatus.PENDING,
      progress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      speed: 0,
      eta: 0
    };

    this.emit('progress', progress);

    try {
      // Create abort controller for this download
      const abortController = new AbortController();
      this.activeDownloads.set(addon.id, abortController);

      // Download the file
      const filePath = await this.downloadFile(addon, downloadUrl, abortController.signal);
      
      // Extract if it's a ZIP file
      const extractedPath = await this.extractIfNeeded(filePath, addon);

      // Update progress to completed
      progress.status = DownloadStatus.COMPLETED;
      progress.progress = 100;
      this.emit('progress', progress);

      return extractedPath;

    } catch (error) {
      progress.status = DownloadStatus.FAILED;
      progress.error = error instanceof Error ? error.message : 'Unknown error';
      this.emit('progress', progress);
      throw error;
    } finally {
      this.activeDownloads.delete(addon.id);
    }
  }

  /**
   * Cancel a download
   */
  cancelDownload(addonId: string): boolean {
    const controller = this.activeDownloads.get(addonId);
    if (controller) {
      controller.abort();
      this.activeDownloads.delete(addonId);
      
      const progress: DownloadProgress = {
        addonId,
        status: DownloadStatus.CANCELLED,
        progress: 0,
        downloadedBytes: 0,
        totalBytes: 0,
        speed: 0,
        eta: 0
      };
      this.emit('progress', progress);
      
      return true;
    }
    return false;
  }

  /**
   * Get download URL from addon
   */
  private getDownloadUrl(addon: Addon): string | null {
    const primaryLink = addon.links.find(link => link.isPrimary);
    return primaryLink?.url || addon.links[0]?.url || null;
  }

  /**
   * Download file with progress tracking
   */
  private async downloadFile(addon: Addon, url: string, signal: AbortSignal): Promise<string> {
    const fileName = this.generateFileName(addon, url);
    const filePath = path.join(this.tempDir, fileName);
    
    let attempt = 0;
    let lastError: Error | null = null;

    while (attempt < this.options.maxRetries) {
      try {
        attempt++;
        
        const response = await axios({
          method: 'GET',
          url,
          responseType: 'stream',
          timeout: this.options.timeoutMs,
          signal,
          headers: {
            'User-Agent': 'TurtleWoW-AddonManager/1.0.0'
          },
          onDownloadProgress: (progressEvent: AxiosProgressEvent) => {
            this.handleDownloadProgress(addon.id, progressEvent);
          }
        });

        // Create write stream
        const writer = fs.createWriteStream(filePath);
        response.data.pipe(writer);

        // Wait for download to complete
        await new Promise<void>((resolve, reject) => {
          writer.on('finish', resolve);
          writer.on('error', reject);
          response.data.on('error', reject);
        });

        // Verify file exists and has content
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
          throw new Error('Downloaded file is empty');
        }

        return filePath;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown download error');
        
        if (signal.aborted) {
          throw new Error('Download cancelled');
        }

        console.warn(`Download attempt ${attempt} failed for ${addon.name}:`, lastError.message);
        
        // Clean up partial file
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }

        // Wait before retry (exponential backoff)
        if (attempt < this.options.maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      }
    }

    throw lastError || new Error('Download failed after all retries');
  }

  /**
   * Handle download progress events
   */
  private handleDownloadProgress(addonId: string, progressEvent: AxiosProgressEvent) {
    const { loaded, total } = progressEvent;
    
    if (!total) return;

    const progress: DownloadProgress = {
      addonId,
      status: DownloadStatus.DOWNLOADING,
      progress: Math.round((loaded / total) * 100),
      downloadedBytes: loaded,
      totalBytes: total,
      speed: 0, // TODO: Calculate speed
      eta: 0    // TODO: Calculate ETA
    };

    this.emit('progress', progress);
  }

  /**
   * Extract ZIP file if needed
   */
  private async extractIfNeeded(filePath: string, addon: Addon): Promise<string> {
    if (!filePath.toLowerCase().endsWith('.zip')) {
      return filePath;
    }

    const progress: DownloadProgress = {
      addonId: addon.id,
      status: DownloadStatus.EXTRACTING,
      progress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      speed: 0,
      eta: 0
    };
    this.emit('progress', progress);

    const extractDir = path.join(this.tempDir, `${addon.id}_extracted`);
    this.ensureDirectoryExists(extractDir);

    return new Promise((resolve, reject) => {
      yauzl.open(filePath, { lazyEntries: true }, (err, zipfile) => {
        if (err) {
          reject(new Error(`Failed to open ZIP file: ${err.message}`));
          return;
        }

        if (!zipfile) {
          reject(new Error('ZIP file is null'));
          return;
        }

        let extractedFiles = 0;
        const totalEntries = zipfile.entryCount;

        zipfile.readEntry();

        zipfile.on('entry', (entry) => {
          if (/\/$/.test(entry.fileName)) {
            // Directory entry
            const dirPath = path.join(extractDir, entry.fileName);
            this.ensureDirectoryExists(dirPath);
            zipfile.readEntry();
          } else {
            // File entry
            zipfile.openReadStream(entry, (err, readStream) => {
              if (err) {
                reject(err);
                return;
              }

              if (!readStream) {
                reject(new Error('Read stream is null'));
                return;
              }

              const filePath = path.join(extractDir, entry.fileName);
              this.ensureDirectoryExists(path.dirname(filePath));

              const writeStream = fs.createWriteStream(filePath);
              readStream.pipe(writeStream);

              writeStream.on('close', () => {
                extractedFiles++;
                
                // Update progress
                progress.progress = Math.round((extractedFiles / totalEntries) * 100);
                this.emit('progress', progress);
                
                zipfile.readEntry();
              });

              writeStream.on('error', reject);
            });
          }
        });

        zipfile.on('end', () => {
          // Clean up original ZIP file
          fs.unlinkSync(filePath);
          resolve(extractDir);
        });

        zipfile.on('error', reject);
      });
    });
  }

  /**
   * Generate filename for download
   */
  private generateFileName(addon: Addon, url: string): string {
    const timestamp = Date.now();
    const urlPath = new URL(url).pathname;
    const extension = path.extname(urlPath) || '.zip';
    
    return `${addon.id}_${timestamp}${extension}`;
  }

  /**
   * Ensure directory exists
   */
  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean up temp files
   */
  cleanup(): void {
    // Cancel all active downloads
    for (const [addonId, controller] of this.activeDownloads) {
      controller.abort();
    }
    this.activeDownloads.clear();

    // Clean up temp directory
    if (fs.existsSync(this.tempDir)) {
      fs.rmSync(this.tempDir, { recursive: true, force: true });
    }
  }
}
