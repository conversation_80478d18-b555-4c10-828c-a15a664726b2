/**
 * Main Addon Manager Service
 * Orchestrates all addon operations
 */

import { EventEmitter } from 'events';
import * as path from 'path';
import * as os from 'os';
import { TurtleWikiScraper } from './scraper/turtleWikiScraper';
import { DownloadEngine } from './downloader/downloadEngine';
import { AddonInstaller } from './installer/addonInstaller';
import { AddonRegistry } from './registry/addonRegistry';
import { LinkAnalyzer } from './linkAnalyzer/linkAnalyzer';
import { 
  Addon, 
  AddonInstallation, 
  AddonUpdate, 
  WoWDirectory, 
  DownloadProgress,
  AppSettings,
  InstallationResult
} from '../types/addon';

export class AddonManager extends EventEmitter {
  private scraper: TurtleWikiScraper;
  private downloadEngine: DownloadEngine;
  private installer: AddonInstaller;
  private registry: AddonRegistry;
  private linkAnalyzer: LinkAnalyzer;
  
  private tempDir: string;
  private dbPath: string;

  constructor(dataDir?: string) {
    super();
    
    // Set up directories
    const baseDir = dataDir || path.join(os.homedir(), '.turtle-addon-manager');
    this.tempDir = path.join(baseDir, 'temp');
    this.dbPath = path.join(baseDir, 'addons.db');
    
    // Initialize services
    this.scraper = new TurtleWikiScraper();
    this.downloadEngine = new DownloadEngine(this.tempDir);
    this.installer = new AddonInstaller();
    this.registry = new AddonRegistry(this.dbPath);
    this.linkAnalyzer = new LinkAnalyzer();
    
    // Forward events
    this.downloadEngine.on('progress', (progress: DownloadProgress) => {
      this.emit('downloadProgress', progress);
    });
    
    this.installer.on('progress', (progress: DownloadProgress) => {
      this.emit('installProgress', progress);
    });
  }

  /**
   * Initialize the addon manager
   */
  async initialize(settings: AppSettings): Promise<void> {
    try {
      console.log('🚀 Initializing TurtleWoW Addon Manager...');
      
      // Set WoW directory if configured
      if (settings.wowDirectory) {
        this.installer.setWoWDirectory(settings.wowDirectory);
      }
      
      console.log('✅ Addon Manager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Addon Manager:', error);
      throw error;
    }
  }

  /**
   * Sync addons from Turtle WoW wiki
   */
  async syncAddons(): Promise<number> {
    try {
      console.log('🔄 Syncing addons from Turtle WoW wiki...');
      this.emit('syncStarted');
      
      const addons = await this.scraper.scrapeAllAddons();
      this.registry.saveAddons(addons);
      
      console.log(`✅ Synced ${addons.length} addons`);
      this.emit('syncCompleted', addons.length);
      
      return addons.length;
    } catch (error) {
      console.error('❌ Failed to sync addons:', error);
      this.emit('syncFailed', error);
      throw error;
    }
  }

  /**
   * Get all addons
   */
  getAllAddons(): Addon[] {
    return this.registry.getAllAddons();
  }

  /**
   * Search addons
   */
  searchAddons(query: string, category?: string, installed?: boolean): Addon[] {
    return this.registry.searchAddons(query, category, installed);
  }

  /**
   * Get addon by ID
   */
  getAddon(id: string): Addon | null {
    return this.registry.getAddon(id);
  }

  /**
   * Get installed addons
   */
  getInstalledAddons(): Addon[] {
    return this.registry.getInstalledAddons();
  }

  /**
   * Install an addon
   */
  async installAddon(addonId: string, createBackup = true): Promise<InstallationResult> {
    try {
      const addon = this.registry.getAddon(addonId);
      if (!addon) {
        return {
          success: false,
          error: 'Addon not found'
        };
      }

      console.log(`📦 Installing addon: ${addon.name}`);
      this.emit('installStarted', addon);

      // Download the addon
      const extractedPath = await this.downloadEngine.downloadAddon(addon);
      
      // Install the addon
      const result = await this.installer.installAddon(addon, extractedPath, createBackup);
      
      if (result.success && result.installation) {
        // Save installation record
        this.registry.saveInstallation(result.installation);
        console.log(`✅ Successfully installed: ${addon.name}`);
        this.emit('installCompleted', addon, result);
      } else {
        console.error(`❌ Failed to install: ${addon.name}`, result.error);
        this.emit('installFailed', addon, result.error);
      }

      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Installation failed:`, error);
      this.emit('installFailed', addonId, errorMessage);
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Uninstall an addon
   */
  async uninstallAddon(addonId: string): Promise<boolean> {
    try {
      const addon = this.registry.getAddon(addonId);
      if (!addon) {
        throw new Error('Addon not found');
      }

      const installation = this.registry.getInstallation(addonId);
      if (!installation) {
        throw new Error('Installation record not found');
      }

      console.log(`🗑️ Uninstalling addon: ${addon.name}`);
      this.emit('uninstallStarted', addon);

      const success = await this.installer.uninstallAddon(installation);
      
      if (success) {
        this.registry.removeInstallation(addonId);
        console.log(`✅ Successfully uninstalled: ${addon.name}`);
        this.emit('uninstallCompleted', addon);
      } else {
        console.error(`❌ Failed to uninstall: ${addon.name}`);
        this.emit('uninstallFailed', addon, 'Uninstallation failed');
      }

      return success;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Uninstallation failed:`, error);
      this.emit('uninstallFailed', addonId, errorMessage);
      return false;
    }
  }

  /**
   * Check for addon updates
   */
  async checkForUpdates(): Promise<AddonUpdate[]> {
    try {
      console.log('🔍 Checking for addon updates...');
      this.emit('updateCheckStarted');
      
      const installedAddons = this.getInstalledAddons();
      const updates: AddonUpdate[] = [];

      for (const addon of installedAddons) {
        try {
          const update = await this.checkAddonUpdate(addon);
          if (update) {
            updates.push(update);
            this.registry.saveUpdate(update);
          }
        } catch (error) {
          console.warn(`Failed to check update for ${addon.name}:`, error);
        }
      }

      console.log(`✅ Found ${updates.length} available updates`);
      this.emit('updateCheckCompleted', updates);
      
      return updates;
      
    } catch (error) {
      console.error('❌ Failed to check for updates:', error);
      this.emit('updateCheckFailed', error);
      throw error;
    }
  }

  /**
   * Check update for a single addon
   */
  private async checkAddonUpdate(addon: Addon): Promise<AddonUpdate | null> {
    if (!addon.installedVersion || addon.links.length === 0) {
      return null;
    }

    const primaryLink = addon.links.find(link => link.isPrimary) || addon.links[0];
    const analysis = await this.linkAnalyzer.analyzeLink(primaryLink.url);

    if (!analysis.version || analysis.version === addon.installedVersion) {
      return null;
    }

    return {
      addonId: addon.id,
      currentVersion: addon.installedVersion,
      availableVersion: analysis.version,
      updateUrl: analysis.downloadUrl || primaryLink.url,
      changelog: analysis.releaseNotes,
      releaseDate: analysis.lastUpdated,
      isBreaking: false // TODO: Implement breaking change detection
    };
  }

  /**
   * Update an addon
   */
  async updateAddon(addonId: string): Promise<InstallationResult> {
    // For now, updating is the same as reinstalling
    return this.installAddon(addonId, true);
  }

  /**
   * Cancel a download
   */
  cancelDownload(addonId: string): boolean {
    return this.downloadEngine.cancelDownload(addonId);
  }

  /**
   * Get available updates
   */
  getAvailableUpdates(): AddonUpdate[] {
    return this.registry.getAvailableUpdates();
  }

  /**
   * Validate WoW directory
   */
  static validateWoWDirectory(dirPath: string): WoWDirectory | null {
    try {
      const fs = require('fs');
      
      // Check if directory exists
      if (!fs.existsSync(dirPath)) {
        return null;
      }

      // Look for WoW executable
      const possibleExes = ['WoW.exe', 'Wow.exe', 'wow.exe'];
      const hasWoWExe = possibleExes.some(exe => 
        fs.existsSync(path.join(dirPath, exe))
      );

      if (!hasWoWExe) {
        return null;
      }

      const addonsPath = path.join(dirPath, 'Interface', 'AddOns');
      
      return {
        path: dirPath,
        isValid: true,
        addonsPath,
        lastChecked: new Date()
      };
      
    } catch (error) {
      console.error('Error validating WoW directory:', error);
      return null;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.downloadEngine.cleanup();
    this.registry.close();
  }
}
