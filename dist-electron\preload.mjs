"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("electronAPI", {
  // IPC methods
  invoke: (channel, ...args) => electron.ipcRenderer.invoke(channel, ...args),
  send: (channel, ...args) => electron.ipcRenderer.send(channel, ...args),
  on: (channel, callback) => {
    const subscription = (_event, ...args) => callback(...args);
    electron.ipcRenderer.on(channel, subscription);
    return () => electron.ipcRenderer.removeListener(channel, subscription);
  },
  // Shell methods
  openExternal: (url) => electron.shell.openExternal(url),
  // Directory selection
  selectDirectory: () => electron.ipcRenderer.invoke("select-directory"),
  // File system operations (will be handled by main process)
  validateWoWDirectory: (path) => electron.ipcRenderer.invoke("validate-wow-directory", path),
  // Addon operations (will be handled by main process)
  scrapeAddons: () => electron.ipcRenderer.invoke("scrape-addons"),
  downloadAddon: (addon) => electron.ipcRenderer.invoke("download-addon", addon),
  installAddon: (addonId, extractedPath) => electron.ipcRenderer.invoke("install-addon", addonId, extractedPath),
  // Database operations
  initDatabase: (dbPath) => electron.ipcRenderer.invoke("init-database", dbPath),
  saveAddon: (addon) => electron.ipcRenderer.invoke("save-addon", addon),
  getAddons: () => electron.ipcRenderer.invoke("get-addons"),
  searchAddons: (query, category, installed) => electron.ipcRenderer.invoke("search-addons", query, category, installed)
});
electron.contextBridge.exposeInMainWorld("require", (module) => {
  const allowedModules = ["electron"];
  if (allowedModules.includes(module)) {
    return { ipcRenderer: electron.ipcRenderer, shell: electron.shell };
  }
  throw new Error(`Module ${module} is not allowed`);
});
