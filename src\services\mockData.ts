/**
 * Mock data for testing the UI
 */

import { Addon, AddonCategory, LinkType, LinkSourceType } from '../types/addon';

export const mockAddons: Addon[] = [
  {
    id: 'questie_123',
    name: '<PERSON><PERSON>',
    description: 'The ultimate quest helper addon for Classic WoW. Shows quest objectives on your map and minimap.',
    category: AddonCategory.LEVELING,
    links: [{
      url: 'https://github.com/AeroScripts/QuestieDev',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '7.2.1',
    author: 'AeroScripts',
    lastUpdated: new Date('2024-01-15'),
    isInstalled: false,
    tags: ['quest', 'map', 'leveling', 'objectives'],
    featured: true,
    downloadCount: 15420,
    rating: 4.8
  },
  {
    id: 'details_456',
    name: 'Details! Damage Meter',
    description: 'Advanced damage meter and combat analysis tool. Track DPS, healing, and threat in real-time.',
    category: AddonCategory.COMBAT,
    links: [{
      url: 'https://github.com/Tercioo/Details-Damage-Meter',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '10.2.5.12345',
    author: 'Tercioo',
    lastUpdated: new Date('2024-01-20'),
    isInstalled: true,
    installedVersion: '10.2.5.12345',
    tags: ['dps', 'damage', 'meter', 'combat', 'analysis'],
    featured: true,
    downloadCount: 23150,
    rating: 4.9
  },
  {
    id: 'elvui_789',
    name: 'ElvUI',
    description: 'Complete UI replacement that transforms your interface with a modern, clean design.',
    category: AddonCategory.UI_REPLACEMENT,
    links: [{
      url: 'https://www.tukui.org/download.php?ui=elvui',
      type: LinkSourceType.CUSTOM_HOST,
      isPrimary: true,
      label: 'Official Download'
    }],
    linkType: LinkType.EXTERNAL_ONLY,
    version: '13.46',
    author: 'Elv',
    lastUpdated: new Date('2024-01-18'),
    isInstalled: false,
    tags: ['ui', 'interface', 'replacement', 'modern', 'clean'],
    featured: true,
    downloadCount: 45230,
    rating: 4.7
  },
  {
    id: 'weakauras_101',
    name: 'WeakAuras 2',
    description: 'Powerful addon for creating custom displays and alerts. Track buffs, debuffs, cooldowns, and more.',
    category: AddonCategory.UTILITY,
    links: [{
      url: 'https://github.com/WeakAuras/WeakAuras2',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '5.5.3',
    author: 'WeakAuras Team',
    lastUpdated: new Date('2024-01-22'),
    isInstalled: true,
    installedVersion: '5.5.2',
    tags: ['auras', 'alerts', 'tracking', 'custom', 'displays'],
    featured: true,
    downloadCount: 38920,
    rating: 4.8
  },
  {
    id: 'bigwigs_202',
    name: 'BigWigs Bossmods',
    description: 'Lightweight boss encounter addon with timers, alerts, and warnings for raid and dungeon bosses.',
    category: AddonCategory.ENDGAME,
    links: [{
      url: 'https://github.com/BigWigsMods/BigWigs',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '3.2.15',
    author: 'BigWigs Team',
    lastUpdated: new Date('2024-01-19'),
    isInstalled: false,
    tags: ['boss', 'raid', 'dungeon', 'timers', 'alerts'],
    featured: false,
    downloadCount: 12840,
    rating: 4.6
  },
  {
    id: 'auctioneer_303',
    name: 'Auctioneer Suite',
    description: 'Comprehensive auction house addon with pricing data, search tools, and automated bidding.',
    category: AddonCategory.UTILITY,
    links: [{
      url: 'https://www.curseforge.com/wow/addons/auctioneer',
      type: LinkSourceType.CURSEFORGE,
      isPrimary: true,
      label: 'CurseForge'
    }],
    linkType: LinkType.EXTERNAL_ONLY,
    version: '5.21.7',
    author: 'Norganna',
    lastUpdated: new Date('2024-01-10'),
    isInstalled: false,
    tags: ['auction', 'house', 'pricing', 'economy', 'gold'],
    featured: false,
    downloadCount: 8750,
    rating: 4.3
  },
  {
    id: 'bartender_404',
    name: 'Bartender4',
    description: 'Action bar replacement addon with full customization of button layouts, keybinds, and visibility.',
    category: AddonCategory.UI_REPLACEMENT,
    links: [{
      url: 'https://github.com/Nevcairiel/Bartender4',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '4.14.3',
    author: 'Nevcairiel',
    lastUpdated: new Date('2024-01-16'),
    isInstalled: true,
    installedVersion: '4.14.3',
    tags: ['action', 'bars', 'ui', 'customization', 'keybinds'],
    featured: false,
    downloadCount: 19650,
    rating: 4.5
  },
  {
    id: 'totalrp_505',
    name: 'Total RP 3',
    description: 'Complete roleplay addon with character profiles, descriptions, and immersive RP tools.',
    category: AddonCategory.ROLEPLAY,
    links: [{
      url: 'https://github.com/Ellypse/Total-RP-3',
      type: LinkSourceType.GITHUB_REPO,
      isPrimary: true,
      label: 'GitHub Repository'
    }],
    linkType: LinkType.DIRECT_DOWNLOAD,
    version: '2.6.7',
    author: 'Ellypse',
    lastUpdated: new Date('2024-01-14'),
    isInstalled: false,
    tags: ['roleplay', 'rp', 'character', 'profile', 'immersion'],
    featured: false,
    downloadCount: 6420,
    rating: 4.7
  }
];

export function getMockAddons(): Addon[] {
  return [...mockAddons];
}

export function getMockAddonById(id: string): Addon | null {
  return mockAddons.find(addon => addon.id === id) || null;
}

export function searchMockAddons(query: string, category?: string, installed?: boolean): Addon[] {
  let filtered = mockAddons;

  // Apply search filter
  if (query) {
    const queryLower = query.toLowerCase();
    filtered = filtered.filter(addon => 
      addon.name.toLowerCase().includes(queryLower) ||
      addon.description.toLowerCase().includes(queryLower) ||
      addon.tags.some(tag => tag.toLowerCase().includes(queryLower))
    );
  }

  // Apply category filter
  if (category && category !== 'all') {
    filtered = filtered.filter(addon => addon.category === category);
  }

  // Apply installed filter
  if (installed !== undefined) {
    filtered = filtered.filter(addon => addon.isInstalled === installed);
  }

  return filtered;
}
