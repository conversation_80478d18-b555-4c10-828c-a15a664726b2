/**
 * Electron-compatible Addon Manager Service
 * Uses IPC to communicate with main process for Node.js operations
 */

import {
  Addon,
  AddonInstallation,
  AddonUpdate,
  WoWDirectory,
  DownloadProgress,
  AppSettings
} from '../types/addon';
import { getMockAddons, getMockAddonById, searchMockAddons } from './mockData';

import '../types/electronAPI';

export class ElectronAddonManager {
  private eventListeners: Map<string, Set<Function>> = new Map();

  /**
   * Initialize the addon manager
   */
  async initialize(settings: AppSettings): Promise<void> {
    try {
      console.log('🚀 Initializing Electron Addon Manager...');
      
      // Initialize database in main process
      const userDataPath = await window.electronAPI.invoke('get-user-data-path');
      const dbPath = `${userDataPath}/addons.db`;
      await window.electronAPI.initDatabase(dbPath);
      
      console.log('✅ Electron Addon Manager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Electron Addon Manager:', error);
      throw error;
    }
  }

  /**
   * Sync addons from Turtle WoW wiki
   */
  async syncAddons(): Promise<number> {
    try {
      console.log('🔄 Syncing addons from Turtle WoW wiki...');
      this.emit('syncStarted');

      // For now, use mock data
      const addons = getMockAddons();

      // Simulate some delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log(`✅ Synced ${addons.length} addons`);
      this.emit('syncCompleted', addons.length);

      return addons.length;
    } catch (error) {
      console.error('❌ Failed to sync addons:', error);
      this.emit('syncFailed', error);
      throw error;
    }
  }

  /**
   * Get all addons
   */
  async getAllAddons(): Promise<Addon[]> {
    return getMockAddons();
  }

  /**
   * Search addons
   */
  async searchAddons(query: string, category?: string, installed?: boolean): Promise<Addon[]> {
    return searchMockAddons(query, category, installed);
  }

  /**
   * Get addon by ID
   */
  async getAddon(id: string): Promise<Addon | null> {
    return getMockAddonById(id);
  }

  /**
   * Get installed addons
   */
  async getInstalledAddons(): Promise<Addon[]> {
    return searchMockAddons('', undefined, true);
  }

  /**
   * Install an addon
   */
  async installAddon(addonId: string, createBackup = true): Promise<any> {
    try {
      const addon = await this.getAddon(addonId);
      if (!addon) {
        return {
          success: false,
          error: 'Addon not found'
        };
      }

      console.log(`📦 Installing addon: ${addon.name}`);
      this.emit('installStarted', addon);

      // Download and install through main process
      const result = await window.electronAPI.invoke('install-addon-full', addonId, createBackup);
      
      if (result.success) {
        console.log(`✅ Successfully installed: ${addon.name}`);
        this.emit('installCompleted', addon, result);
      } else {
        console.error(`❌ Failed to install: ${addon.name}`, result.error);
        this.emit('installFailed', addon, result.error);
      }

      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Installation failed:`, error);
      this.emit('installFailed', addonId, errorMessage);
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Uninstall an addon
   */
  async uninstallAddon(addonId: string): Promise<boolean> {
    try {
      const addon = await this.getAddon(addonId);
      if (!addon) {
        throw new Error('Addon not found');
      }

      console.log(`🗑️ Uninstalling addon: ${addon.name}`);
      this.emit('uninstallStarted', addon);

      const success = await window.electronAPI.invoke('uninstall-addon', addonId);
      
      if (success) {
        console.log(`✅ Successfully uninstalled: ${addon.name}`);
        this.emit('uninstallCompleted', addon);
      } else {
        console.error(`❌ Failed to uninstall: ${addon.name}`);
        this.emit('uninstallFailed', addon, 'Uninstallation failed');
      }

      return success;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Uninstallation failed:`, error);
      this.emit('uninstallFailed', addonId, errorMessage);
      return false;
    }
  }

  /**
   * Check for addon updates
   */
  async checkForUpdates(): Promise<AddonUpdate[]> {
    try {
      console.log('🔍 Checking for addon updates...');
      this.emit('updateCheckStarted');
      
      const updates = await window.electronAPI.invoke('check-updates');
      
      console.log(`✅ Found ${updates.length} available updates`);
      this.emit('updateCheckCompleted', updates);
      
      return updates;
      
    } catch (error) {
      console.error('❌ Failed to check for updates:', error);
      this.emit('updateCheckFailed', error);
      throw error;
    }
  }

  /**
   * Update an addon
   */
  async updateAddon(addonId: string): Promise<any> {
    // For now, updating is the same as reinstalling
    return this.installAddon(addonId, true);
  }

  /**
   * Cancel a download
   */
  async cancelDownload(addonId: string): Promise<boolean> {
    return await window.electronAPI.invoke('cancel-download', addonId);
  }

  /**
   * Get available updates
   */
  async getAvailableUpdates(): Promise<AddonUpdate[]> {
    return await window.electronAPI.invoke('get-available-updates');
  }

  /**
   * Validate WoW directory
   */
  static async validateWoWDirectory(dirPath: string): Promise<WoWDirectory | null> {
    return await window.electronAPI.validateWoWDirectory(dirPath);
  }

  /**
   * Event emitter functionality
   */
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.eventListeners.clear();
    // Main process cleanup will be handled by main process
    window.electronAPI.invoke('cleanup-addon-manager');
  }
}
