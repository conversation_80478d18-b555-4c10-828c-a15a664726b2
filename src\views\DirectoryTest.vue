<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Directory Selection Test</h1>
    
    <div class="space-y-4">
      <button @click="testElectronAPI" class="btn-primary">
        Test Electron API
      </button>
      
      <button @click="testDirectorySelection" class="btn-secondary">
        Test Directory Selection
      </button>
      
      <button @click="testValidation" class="btn-secondary">
        Test Validation (with hardcoded path)
      </button>
      
      <div v-if="result" class="p-4 bg-gray-100 dark:bg-gray-800 rounded">
        <h3 class="font-bold">Result:</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
      
      <div v-if="error" class="p-4 bg-red-100 dark:bg-red-900 rounded">
        <h3 class="font-bold text-red-800 dark:text-red-200">Error:</h3>
        <p class="text-red-600 dark:text-red-400">{{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const result = ref<any>(null)
const error = ref<string>('')

function testElectronAPI() {
  console.log('🧪 Testing Electron API...')
  result.value = null
  error.value = ''
  
  try {
    console.log('🧪 window.electronAPI exists:', !!window.electronAPI)
    
    if (window.electronAPI) {
      console.log('🧪 Available methods:', Object.keys(window.electronAPI))
      result.value = {
        available: true,
        methods: Object.keys(window.electronAPI)
      }
    } else {
      error.value = 'Electron API not available'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error'
  }
}

async function testDirectorySelection() {
  console.log('📁 Testing directory selection...')
  result.value = null
  error.value = ''
  
  try {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }
    
    console.log('📁 Calling selectDirectory...')
    const dialogResult = await window.electronAPI.selectDirectory()
    console.log('📁 Dialog result:', dialogResult)
    
    result.value = dialogResult
  } catch (err) {
    console.error('❌ Directory selection failed:', err)
    error.value = err instanceof Error ? err.message : 'Unknown error'
  }
}

async function testValidation() {
  console.log('🔍 Testing validation with hardcoded path...')
  result.value = null
  error.value = ''
  
  try {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }
    
    // Test with a common path
    const testPath = 'C:\\Program Files\\World of Warcraft'
    console.log('🔍 Testing path:', testPath)
    
    const validation = await window.electronAPI.validateWoWDirectory(testPath)
    console.log('🔍 Validation result:', validation)
    
    result.value = validation
  } catch (err) {
    console.error('❌ Validation failed:', err)
    error.value = err instanceof Error ? err.message : 'Unknown error'
  }
}
</script>
