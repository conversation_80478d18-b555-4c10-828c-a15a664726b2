<template>
  <div class="h-full flex flex-col">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Browse Addons</h1>
        <div class="flex items-center space-x-4">
          <!-- View Toggle -->
          <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              @click="viewMode = 'grid'"
              :class="viewMode === 'grid' ? 'bg-white dark:bg-gray-600 shadow-sm' : ''"
              class="p-2 rounded-md transition-colors"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
              </svg>
            </button>
            <button
              @click="viewMode = 'list'"
              :class="viewMode === 'list' ? 'bg-white dark:bg-gray-600 shadow-sm' : ''"
              class="p-2 rounded-md transition-colors"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- Search -->
        <div class="flex-1">
          <div class="relative">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
            <input
              v-model="addonStore.searchQuery"
              type="text"
              placeholder="Search addons..."
              class="input-field pl-10"
            />
          </div>
        </div>

        <!-- Category Filter -->
        <select v-model="addonStore.selectedCategory" class="input-field w-48">
          <option value="all">All Categories</option>
          <option value="featured">Featured</option>
          <option value="ui_replacement">UI Replacement</option>
          <option value="leveling">Leveling</option>
          <option value="endgame">Endgame</option>
          <option value="combat">Combat</option>
          <option value="utility">Utility</option>
          <option value="roleplay">Roleplay</option>
          <option value="profession">Profession</option>
          <option value="pvp">PvP</option>
          <option value="library">Library</option>
          <option value="other">Other</option>
        </select>

        <!-- Installed Filter -->
        <button
          @click="addonStore.toggleInstalledOnly()"
          :class="addonStore.showInstalledOnly ? 'btn-primary' : 'btn-secondary'"
          class="whitespace-nowrap"
        >
          {{ addonStore.showInstalledOnly ? 'Show All' : 'Installed Only' }}
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-auto p-6">
      <!-- Loading State -->
      <div v-if="addonStore.isLoading" class="flex items-center justify-center h-64">
        <div class="text-center">
          <svg class="animate-spin w-8 h-8 text-primary-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
          </svg>
          <p class="text-gray-600 dark:text-gray-400">Loading addons...</p>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="addonStore.filteredAddons.length === 0" class="text-center py-12">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No addons found</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          {{ addonStore.searchQuery ? 'Try adjusting your search terms' : 'Try syncing addons from the wiki' }}
        </p>
        <button
          v-if="!addonStore.searchQuery"
          @click="addonStore.syncAddons()"
          class="btn-primary"
        >
          Sync Addons
        </button>
      </div>

      <!-- Addon Grid/List -->
      <div v-else>
        <!-- Stats -->
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          Showing {{ addonStore.filteredAddons.length }} of {{ addonStore.addons.length }} addons
        </div>

        <!-- Grid View -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AddonCard
            v-for="addon in addonStore.filteredAddons"
            :key="addon.id"
            :addon="addon"
            @install="handleInstall"
            @uninstall="handleUninstall"
            @view-details="viewDetails"
          />
        </div>

        <!-- List View -->
        <div v-else class="space-y-4">
          <AddonListItem
            v-for="addon in addonStore.filteredAddons"
            :key="addon.id"
            :addon="addon"
            @install="handleInstall"
            @uninstall="handleUninstall"
            @view-details="viewDetails"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAddonStore } from '../stores/addonStore'
import AddonCard from '../components/AddonCard.vue'
import AddonListItem from '../components/AddonListItem.vue'

const router = useRouter()
const addonStore = useAddonStore()

const viewMode = ref<'grid' | 'list'>('grid')

function handleInstall(addonId: string) {
  addonStore.installAddon(addonId)
}

function handleUninstall(addonId: string) {
  addonStore.uninstallAddon(addonId)
}

function viewDetails(addonId: string) {
  router.push({ name: 'addon-details', params: { id: addonId } })
}
</script>
