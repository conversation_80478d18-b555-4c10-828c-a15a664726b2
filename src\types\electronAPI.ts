/**
 * Type definitions for Electron API
 */

import { Addon, WoWDirectory, AddonUpdate } from './addon';

export interface ElectronAPI {
  // IPC methods
  invoke: (channel: string, ...args: any[]) => Promise<any>
  send: (channel: string, ...args: any[]) => void
  on: (channel: string, callback: (...args: any[]) => void) => () => void
  
  // Shell methods
  openExternal: (url: string) => Promise<void>
  
  // Directory selection
  selectDirectory: () => Promise<{ canceled: boolean; filePaths: string[] }>
  
  // File system operations
  validateWoWDirectory: (path: string) => Promise<WoWDirectory | null>
  
  // Addon operations
  scrapeAddons: () => Promise<Addon[]>
  downloadAddon: (addon: Addon) => Promise<string>
  installAddon: (addonId: string, extractedPath: string) => Promise<any>
  
  // Database operations
  initDatabase: (dbPath: string) => Promise<void>
  saveAddon: (addon: Addon) => Promise<void>
  getAddons: () => Promise<Addon[]>
  searchAddons: (query: string, category?: string, installed?: boolean) => Promise<Addon[]>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    require: (module: string) => any
  }
}
