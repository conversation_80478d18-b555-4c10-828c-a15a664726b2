/**
 * <PERSON>don Installer
 * Handles installation, validation, and management of WoW addons
 */

import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { Addon, AddonInstallation, WoWDirectory, DownloadProgress, DownloadStatus } from '../../types/addon';

export interface InstallationResult {
  success: boolean;
  installation?: AddonInstallation;
  error?: string;
  warnings?: string[];
}

export class AddonInstaller extends EventEmitter {
  private wowDirectory: WoWDirectory | null = null;

  constructor(wowDirectory?: WoWDirectory) {
    super();
    if (wowDirectory) {
      this.setWoWDirectory(wowDirectory);
    }
  }

  /**
   * Set WoW directory
   */
  setWoWDirectory(wowDirectory: WoWDirectory): void {
    this.wowDirectory = wowDirectory;
  }

  /**
   * Install an addon from extracted files
   */
  async installAddon(addon: Addon, extractedPath: string, createBackup = true): Promise<InstallationResult> {
    if (!this.wowDirectory) {
      return {
        success: false,
        error: 'WoW directory not configured'
      };
    }

    const progress: DownloadProgress = {
      addonId: addon.id,
      status: DownloadStatus.INSTALLING,
      progress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      speed: 0,
      eta: 0
    };
    this.emit('progress', progress);

    try {
      // Validate WoW directory
      if (!this.validateWoWDirectory()) {
        return {
          success: false,
          error: 'Invalid WoW directory'
        };
      }

      // Find addon folders in extracted path
      const addonFolders = await this.findAddonFolders(extractedPath);
      
      if (addonFolders.length === 0) {
        return {
          success: false,
          error: 'No valid addon folders found in downloaded files'
        };
      }

      const warnings: string[] = [];
      const installedFiles: string[] = [];

      // Create backup if requested
      let backupPath: string | undefined;
      if (createBackup) {
        backupPath = await this.createBackup(addon, addonFolders);
      }

      // Install each addon folder
      for (let i = 0; i < addonFolders.length; i++) {
        const folderInfo = addonFolders[i];
        
        try {
          const files = await this.installAddonFolder(folderInfo, addon);
          installedFiles.push(...files);
          
          // Update progress
          progress.progress = Math.round(((i + 1) / addonFolders.length) * 100);
          this.emit('progress', progress);
          
        } catch (error) {
          warnings.push(`Failed to install folder ${folderInfo.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (installedFiles.length === 0) {
        return {
          success: false,
          error: 'No files were successfully installed',
          warnings
        };
      }

      // Create installation record
      const installation: AddonInstallation = {
        addonId: addon.id,
        version: addon.version || 'unknown',
        installDate: new Date(),
        installPath: this.wowDirectory.addonsPath,
        files: installedFiles,
        backupPath
      };

      // Final progress update
      progress.status = DownloadStatus.COMPLETED;
      progress.progress = 100;
      this.emit('progress', progress);

      return {
        success: true,
        installation,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      progress.status = DownloadStatus.FAILED;
      progress.error = error instanceof Error ? error.message : 'Installation failed';
      this.emit('progress', progress);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown installation error'
      };
    }
  }

  /**
   * Uninstall an addon
   */
  async uninstallAddon(installation: AddonInstallation): Promise<boolean> {
    try {
      // Remove installed files
      for (const file of installation.files) {
        const fullPath = path.join(installation.installPath, file);
        if (fs.existsSync(fullPath)) {
          const stats = fs.statSync(fullPath);
          if (stats.isDirectory()) {
            fs.rmSync(fullPath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(fullPath);
          }
        }
      }

      // Clean up empty directories
      await this.cleanupEmptyDirectories(installation.installPath);

      return true;
    } catch (error) {
      console.error('Failed to uninstall addon:', error);
      return false;
    }
  }

  /**
   * Validate WoW directory
   */
  private validateWoWDirectory(): boolean {
    if (!this.wowDirectory) return false;
    
    const { path: wowPath, addonsPath } = this.wowDirectory;
    
    // Check if WoW directory exists
    if (!fs.existsSync(wowPath)) return false;
    
    // Check if Interface/AddOns directory exists or can be created
    if (!fs.existsSync(addonsPath)) {
      try {
        fs.mkdirSync(addonsPath, { recursive: true });
      } catch {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Find addon folders in extracted path
   */
  private async findAddonFolders(extractedPath: string): Promise<AddonFolderInfo[]> {
    const addonFolders: AddonFolderInfo[] = [];
    
    const searchForAddons = (dirPath: string, depth = 0): void => {
      if (depth > 3) return; // Limit search depth
      
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          // Check if this directory contains a .toc file
          const tocFiles = items.filter(file => file.endsWith('.toc'));
          
          if (tocFiles.length > 0) {
            // This is an addon folder
            const tocFile = tocFiles[0];
            const expectedFolderName = path.basename(tocFile, '.toc');
            
            addonFolders.push({
              name: item,
              path: itemPath,
              tocFile,
              expectedName: expectedFolderName,
              needsRename: item !== expectedFolderName
            });
          } else {
            // Search deeper
            searchForAddons(itemPath, depth + 1);
          }
        }
      }
    };
    
    searchForAddons(extractedPath);
    return addonFolders;
  }

  /**
   * Install a single addon folder
   */
  private async installAddonFolder(folderInfo: AddonFolderInfo, addon: Addon): Promise<string[]> {
    if (!this.wowDirectory) {
      throw new Error('WoW directory not configured');
    }

    const targetName = folderInfo.expectedName;
    const targetPath = path.join(this.wowDirectory.addonsPath, targetName);
    const installedFiles: string[] = [];

    // Remove existing installation if it exists
    if (fs.existsSync(targetPath)) {
      fs.rmSync(targetPath, { recursive: true, force: true });
    }

    // Copy the addon folder
    await this.copyDirectory(folderInfo.path, targetPath);
    
    // Get list of installed files
    const getFiles = (dirPath: string, relativeTo: string): string[] => {
      const files: string[] = [];
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const relativePath = path.relative(relativeTo, itemPath);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          files.push(relativePath);
          files.push(...getFiles(itemPath, relativeTo));
        } else {
          files.push(relativePath);
        }
      }
      
      return files;
    };
    
    installedFiles.push(...getFiles(targetPath, this.wowDirectory.addonsPath));
    
    return installedFiles;
  }

  /**
   * Copy directory recursively
   */
  private async copyDirectory(src: string, dest: string): Promise<void> {
    // Create destination directory
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    for (const item of items) {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      const stats = fs.statSync(srcPath);
      
      if (stats.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  /**
   * Create backup of existing addon
   */
  private async createBackup(addon: Addon, addonFolders: AddonFolderInfo[]): Promise<string | undefined> {
    if (!this.wowDirectory) return undefined;

    const backupDir = path.join(this.wowDirectory.path, 'AddonBackups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `${addon.id}_${timestamp}`);

    let hasBackup = false;

    for (const folderInfo of addonFolders) {
      const existingPath = path.join(this.wowDirectory.addonsPath, folderInfo.expectedName);
      
      if (fs.existsSync(existingPath)) {
        const backupFolderPath = path.join(backupPath, folderInfo.expectedName);
        await this.copyDirectory(existingPath, backupFolderPath);
        hasBackup = true;
      }
    }

    return hasBackup ? backupPath : undefined;
  }

  /**
   * Clean up empty directories
   */
  private async cleanupEmptyDirectories(basePath: string): Promise<void> {
    const items = fs.readdirSync(basePath);
    
    for (const item of items) {
      const itemPath = path.join(basePath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        await this.cleanupEmptyDirectories(itemPath);
        
        // Check if directory is now empty
        const remainingItems = fs.readdirSync(itemPath);
        if (remainingItems.length === 0) {
          fs.rmdirSync(itemPath);
        }
      }
    }
  }
}

interface AddonFolderInfo {
  name: string;
  path: string;
  tocFile: string;
  expectedName: string;
  needsRename: boolean;
}
