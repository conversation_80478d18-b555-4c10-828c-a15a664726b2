/**
 * Link Intelligence System
 * Analyzes addon links to determine if they're direct downloads or external-only
 */

import axios from 'axios';
import { LinkType, LinkSourceType, GitHubRelease, GitLabRelease } from '../../types/addon';

export interface LinkAnalysisResult {
  linkType: LinkType;
  sourceType: LinkSourceType;
  isDirectDownload: boolean;
  downloadUrl?: string;
  version?: string;
  author?: string;
  lastUpdated?: Date;
  releaseNotes?: string;
}

export class LinkAnalyzer {
  private static readonly GITHUB_API_BASE = 'https://api.github.com';
  private static readonly GITLAB_API_BASE = 'https://gitlab.com/api/v4';
  private static readonly USER_AGENT = 'TurtleWoW-AddonManager/1.0.0';

  /**
   * Analyze a link to determine its type and download capabilities
   */
  async analyzeLink(url: string): Promise<LinkAnalysisResult> {
    try {
      const sourceType = this.identifySourceType(url);
      
      switch (sourceType) {
        case LinkSourceType.GITHUB_REPO:
          return await this.analyzeGitHubRepo(url);
        case LinkSourceType.GITLAB_REPO:
          return await this.analyzeGitLabRepo(url);
        case LinkSourceType.DIRECT_ZIP:
          return await this.analyzeDirectZip(url);
        case LinkSourceType.CURSEFORGE:
          return this.analyzeCurseForge(url);
        default:
          return this.analyzeGenericLink(url, sourceType);
      }
    } catch (error) {
      console.warn(`Failed to analyze link: ${url}`, error);
      return {
        linkType: LinkType.EXTERNAL_ONLY,
        sourceType: LinkSourceType.UNKNOWN,
        isDirectDownload: false
      };
    }
  }

  /**
   * Identify the source type of a URL
   */
  private identifySourceType(url: string): LinkSourceType {
    const urlLower = url.toLowerCase();
    
    // GitHub patterns
    if (urlLower.includes('github.com')) {
      if (urlLower.includes('/releases/')) {
        return LinkSourceType.GITHUB_RELEASE;
      }
      return LinkSourceType.GITHUB_REPO;
    }
    
    // GitLab patterns
    if (urlLower.includes('gitlab.com')) {
      if (urlLower.includes('/-/releases/')) {
        return LinkSourceType.GITLAB_RELEASE;
      }
      return LinkSourceType.GITLAB_REPO;
    }
    
    // Direct ZIP files
    if (urlLower.endsWith('.zip') || urlLower.includes('.zip?')) {
      return LinkSourceType.DIRECT_ZIP;
    }
    
    // CurseForge
    if (urlLower.includes('curseforge.com') || urlLower.includes('curse.com')) {
      return LinkSourceType.CURSEFORGE;
    }
    
    // Forum posts
    if (urlLower.includes('forum') || urlLower.includes('board') || urlLower.includes('discussion')) {
      return LinkSourceType.FORUM_POST;
    }
    
    return LinkSourceType.UNKNOWN;
  }

  /**
   * Analyze GitHub repository
   */
  private async analyzeGitHubRepo(url: string): Promise<LinkAnalysisResult> {
    try {
      const repoMatch = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
      if (!repoMatch) {
        throw new Error('Invalid GitHub URL format');
      }
      
      const [, owner, repo] = repoMatch;
      const cleanRepo = repo.replace(/\.git$/, '');
      
      // Check for releases
      const releasesUrl = `${LinkAnalyzer.GITHUB_API_BASE}/repos/${owner}/${cleanRepo}/releases/latest`;
      
      try {
        const response = await axios.get<GitHubRelease>(releasesUrl, {
          headers: { 'User-Agent': LinkAnalyzer.USER_AGENT },
          timeout: 10000
        });
        
        const release = response.data;
        const zipAsset = release.assets.find(asset => 
          asset.name.endsWith('.zip') || asset.content_type === 'application/zip'
        );
        
        if (zipAsset) {
          return {
            linkType: LinkType.DIRECT_DOWNLOAD,
            sourceType: LinkSourceType.GITHUB_RELEASE,
            isDirectDownload: true,
            downloadUrl: zipAsset.browser_download_url,
            version: release.tag_name,
            author: owner,
            lastUpdated: new Date(release.published_at),
            releaseNotes: release.body
          };
        }
      } catch (releaseError) {
        // No releases found, fall back to repo ZIP
        console.log(`No releases found for ${owner}/${cleanRepo}, using repo ZIP`);
      }
      
      // Fall back to repository ZIP download
      const repoZipUrl = `https://github.com/${owner}/${cleanRepo}/archive/refs/heads/main.zip`;
      
      return {
        linkType: LinkType.DIRECT_DOWNLOAD,
        sourceType: LinkSourceType.GITHUB_REPO,
        isDirectDownload: true,
        downloadUrl: repoZipUrl,
        author: owner
      };
      
    } catch (error) {
      console.warn('Failed to analyze GitHub repo:', error);
      return {
        linkType: LinkType.EXTERNAL_ONLY,
        sourceType: LinkSourceType.GITHUB_REPO,
        isDirectDownload: false
      };
    }
  }

  /**
   * Analyze GitLab repository
   */
  private async analyzeGitLabRepo(url: string): Promise<LinkAnalysisResult> {
    try {
      const repoMatch = url.match(/gitlab\.com\/([^\/]+)\/([^\/]+)/);
      if (!repoMatch) {
        throw new Error('Invalid GitLab URL format');
      }
      
      const [, owner, repo] = repoMatch;
      const projectPath = encodeURIComponent(`${owner}/${repo}`);
      
      // Check for releases
      const releasesUrl = `${LinkAnalyzer.GITLAB_API_BASE}/projects/${projectPath}/releases`;
      
      try {
        const response = await axios.get<GitLabRelease[]>(releasesUrl, {
          headers: { 'User-Agent': LinkAnalyzer.USER_AGENT },
          timeout: 10000
        });
        
        if (response.data.length > 0) {
          const latestRelease = response.data[0];
          const zipLink = latestRelease.assets.links.find(link => 
            link.name.toLowerCase().includes('zip') || link.url.endsWith('.zip')
          );
          
          if (zipLink) {
            return {
              linkType: LinkType.DIRECT_DOWNLOAD,
              sourceType: LinkSourceType.GITLAB_RELEASE,
              isDirectDownload: true,
              downloadUrl: zipLink.url,
              version: latestRelease.tag_name,
              author: owner,
              lastUpdated: new Date(latestRelease.created_at),
              releaseNotes: latestRelease.description
            };
          }
        }
      } catch (releaseError) {
        console.log(`No releases found for ${owner}/${repo}, using repo ZIP`);
      }
      
      // Fall back to repository ZIP download
      const repoZipUrl = `https://gitlab.com/${owner}/${repo}/-/archive/main/${repo}-main.zip`;
      
      return {
        linkType: LinkType.DIRECT_DOWNLOAD,
        sourceType: LinkSourceType.GITLAB_REPO,
        isDirectDownload: true,
        downloadUrl: repoZipUrl,
        author: owner
      };
      
    } catch (error) {
      console.warn('Failed to analyze GitLab repo:', error);
      return {
        linkType: LinkType.EXTERNAL_ONLY,
        sourceType: LinkSourceType.GITLAB_REPO,
        isDirectDownload: false
      };
    }
  }

  /**
   * Analyze direct ZIP link
   */
  private async analyzeDirectZip(url: string): Promise<LinkAnalysisResult> {
    try {
      // Verify the ZIP file exists and is accessible
      const response = await axios.head(url, {
        timeout: 10000,
        headers: { 'User-Agent': LinkAnalyzer.USER_AGENT }
      });
      
      const contentType = response.headers['content-type'];
      const isZip = contentType?.includes('zip') || 
                   contentType?.includes('application/octet-stream') ||
                   url.toLowerCase().endsWith('.zip');
      
      if (isZip) {
        return {
          linkType: LinkType.DIRECT_DOWNLOAD,
          sourceType: LinkSourceType.DIRECT_ZIP,
          isDirectDownload: true,
          downloadUrl: url
        };
      }
      
      return {
        linkType: LinkType.EXTERNAL_ONLY,
        sourceType: LinkSourceType.DIRECT_ZIP,
        isDirectDownload: false
      };
      
    } catch (error) {
      return {
        linkType: LinkType.EXTERNAL_ONLY,
        sourceType: LinkSourceType.DIRECT_ZIP,
        isDirectDownload: false
      };
    }
  }

  /**
   * Analyze CurseForge link
   */
  private analyzeCurseForge(url: string): LinkAnalysisResult {
    // CurseForge links typically require manual download
    return {
      linkType: LinkType.EXTERNAL_ONLY,
      sourceType: LinkSourceType.CURSEFORGE,
      isDirectDownload: false
    };
  }

  /**
   * Analyze generic link
   */
  private analyzeGenericLink(url: string, sourceType: LinkSourceType): LinkAnalysisResult {
    return {
      linkType: LinkType.EXTERNAL_ONLY,
      sourceType,
      isDirectDownload: false
    };
  }

  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Extract repository info from GitHub/GitLab URLs
   */
  static extractRepoInfo(url: string): { owner: string; repo: string } | null {
    const githubMatch = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (githubMatch) {
      return { owner: githubMatch[1], repo: githubMatch[2].replace(/\.git$/, '') };
    }
    
    const gitlabMatch = url.match(/gitlab\.com\/([^\/]+)\/([^\/]+)/);
    if (gitlabMatch) {
      return { owner: gitlabMatch[1], repo: gitlabMatch[2].replace(/\.git$/, '') };
    }
    
    return null;
  }
}
