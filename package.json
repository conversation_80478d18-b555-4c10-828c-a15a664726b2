{"name": "turtle-addon-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build && electron-builder", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "better-sqlite3": "^12.2.0", "cheerio": "^1.1.2", "electron-store": "^10.1.0", "node-fetch": "^3.3.2", "pinia": "^3.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vue": "^3.4.21", "vue-router": "^4.5.1", "yauzl": "^3.2.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/cheerio": "^0.22.35", "@types/yauzl": "^2.10.3", "@vitejs/plugin-vue": "^5.0.4", "electron": "^30.0.1", "electron-builder": "^24.13.3", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^2.0.26"}, "main": "dist-electron/main.js"}