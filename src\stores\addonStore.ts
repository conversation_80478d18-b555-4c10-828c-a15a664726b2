/**
 * Pinia store for addon management
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ElectronAddonManager } from '../services/electronAddonManager';
import {
  Addon,
  AddonCategory,
  DownloadProgress,
  AddonUpdate,
  AppSettings,
  AppError,
  ErrorType
} from '../types/addon';

export const useAddonStore = defineStore('addon', () => {
  // State
  const addons = ref<Addon[]>([]);
  const downloads = ref<Map<string, DownloadProgress>>(new Map());
  const updates = ref<AddonUpdate[]>([]);
  const isLoading = ref(false);
  const isSyncing = ref(false);
  const lastSync = ref<Date | null>(null);
  const errors = ref<AppError[]>([]);
  
  // Search and filter state
  const searchQuery = ref('');
  const selectedCategory = ref<AddonCategory | 'all'>('all');
  const showInstalledOnly = ref(false);
  const sortBy = ref<'name' | 'category' | 'updated'>('name');
  const sortOrder = ref<'asc' | 'desc'>('asc');

  // Addon Manager instance
  let addonManager: ElectronAddonManager | null = null;

  // Computed
  const filteredAddons = computed(() => {
    let filtered = addons.value;

    // Apply search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(addon => 
        addon.name.toLowerCase().includes(query) ||
        addon.description.toLowerCase().includes(query) ||
        addon.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory.value !== 'all') {
      filtered = filtered.filter(addon => addon.category === selectedCategory.value);
    }

    // Apply installed filter
    if (showInstalledOnly.value) {
      filtered = filtered.filter(addon => addon.isInstalled);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy.value) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
        case 'updated':
          const aDate = a.lastUpdated?.getTime() || 0;
          const bDate = b.lastUpdated?.getTime() || 0;
          comparison = aDate - bDate;
          break;
      }
      
      return sortOrder.value === 'asc' ? comparison : -comparison;
    });

    return filtered;
  });

  const installedAddons = computed(() => 
    addons.value.filter(addon => addon.isInstalled)
  );

  const featuredAddons = computed(() => 
    addons.value.filter(addon => addon.featured)
  );

  const availableUpdates = computed(() => updates.value.length);

  const activeDownloads = computed(() => 
    Array.from(downloads.value.values()).filter(d => 
      d.status === 'downloading' || d.status === 'extracting' || d.status === 'installing'
    )
  );

  // Actions
  async function initialize(settings: AppSettings) {
    try {
      isLoading.value = true;
      
      addonManager = new ElectronAddonManager();
      await addonManager.initialize(settings);
      
      // Set up event listeners
      setupEventListeners();
      
      // Load existing addons from database
      await loadAddons();
      
    } catch (error) {
      addError('Failed to initialize addon manager', error);
    } finally {
      isLoading.value = false;
    }
  }

  function setupEventListeners() {
    if (!addonManager) return;

    addonManager.on('downloadProgress', (progress: DownloadProgress) => {
      downloads.value.set(progress.addonId, progress);
    });

    addonManager.on('installProgress', (progress: DownloadProgress) => {
      downloads.value.set(progress.addonId, progress);
    });

    addonManager.on('syncStarted', () => {
      isSyncing.value = true;
    });

    addonManager.on('syncCompleted', (count: number) => {
      isSyncing.value = false;
      lastSync.value = new Date();
      loadAddons(); // Reload addons after sync
    });

    addonManager.on('syncFailed', (error: Error) => {
      isSyncing.value = false;
      addError('Failed to sync addons', error);
    });

    addonManager.on('installCompleted', (addon: Addon) => {
      downloads.value.delete(addon.id);
      updateAddonInList(addon.id, { isInstalled: true });
    });

    addonManager.on('installFailed', (addonId: string, error: string) => {
      downloads.value.delete(addonId);
      addError(`Failed to install addon`, new Error(error));
    });

    addonManager.on('uninstallCompleted', (addon: Addon) => {
      updateAddonInList(addon.id, { isInstalled: false });
    });
  }

  async function loadAddons() {
    if (!addonManager) return;

    try {
      const allAddons = await addonManager.getAllAddons();
      addons.value = allAddons;
    } catch (error) {
      addError('Failed to load addons', error);
    }
  }

  async function syncAddons() {
    if (!addonManager) return;
    
    try {
      await addonManager.syncAddons();
    } catch (error) {
      addError('Failed to sync addons', error);
    }
  }

  async function installAddon(addonId: string) {
    if (!addonManager) return;
    
    try {
      const result = await addonManager.installAddon(addonId);
      if (!result.success) {
        addError(`Failed to install addon: ${result.error}`, new Error(result.error));
      }
    } catch (error) {
      addError('Failed to install addon', error);
    }
  }

  async function uninstallAddon(addonId: string) {
    if (!addonManager) return;
    
    try {
      const success = await addonManager.uninstallAddon(addonId);
      if (!success) {
        addError('Failed to uninstall addon', new Error('Uninstallation failed'));
      }
    } catch (error) {
      addError('Failed to uninstall addon', error);
    }
  }

  async function updateAddon(addonId: string) {
    if (!addonManager) return;
    
    try {
      const result = await addonManager.updateAddon(addonId);
      if (!result.success) {
        addError(`Failed to update addon: ${result.error}`, new Error(result.error));
      }
    } catch (error) {
      addError('Failed to update addon', error);
    }
  }

  async function checkForUpdates() {
    if (!addonManager) return;
    
    try {
      const availableUpdates = await addonManager.checkForUpdates();
      updates.value = availableUpdates;
    } catch (error) {
      addError('Failed to check for updates', error);
    }
  }

  function cancelDownload(addonId: string) {
    if (!addonManager) return;
    
    const success = addonManager.cancelDownload(addonId);
    if (success) {
      downloads.value.delete(addonId);
    }
  }

  function getAddon(id: string): Addon | undefined {
    return addons.value.find(addon => addon.id === id);
  }

  function getDownloadProgress(addonId: string): DownloadProgress | undefined {
    return downloads.value.get(addonId);
  }

  function updateAddonInList(addonId: string, updates: Partial<Addon>) {
    const index = addons.value.findIndex(addon => addon.id === addonId);
    if (index !== -1) {
      addons.value[index] = { ...addons.value[index], ...updates };
    }
  }

  function addError(message: string, error: unknown) {
    const appError: AppError = {
      id: Date.now().toString(),
      type: ErrorType.UNKNOWN,
      message,
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date(),
      dismissed: false
    };
    
    errors.value.push(appError);
    console.error(message, error);
  }

  function dismissError(errorId: string) {
    const error = errors.value.find(e => e.id === errorId);
    if (error) {
      error.dismissed = true;
    }
  }

  function clearErrors() {
    errors.value = [];
  }

  // Search and filter actions
  function setSearchQuery(query: string) {
    searchQuery.value = query;
  }

  function setCategory(category: AddonCategory | 'all') {
    selectedCategory.value = category;
  }

  function toggleInstalledOnly() {
    showInstalledOnly.value = !showInstalledOnly.value;
  }

  function setSorting(by: 'name' | 'category' | 'updated', order: 'asc' | 'desc') {
    sortBy.value = by;
    sortOrder.value = order;
  }

  function cleanup() {
    if (addonManager) {
      addonManager.cleanup();
    }
  }

  return {
    // State
    addons,
    downloads,
    updates,
    isLoading,
    isSyncing,
    lastSync,
    errors,
    searchQuery,
    selectedCategory,
    showInstalledOnly,
    sortBy,
    sortOrder,
    
    // Computed
    filteredAddons,
    installedAddons,
    featuredAddons,
    availableUpdates,
    activeDownloads,
    
    // Actions
    initialize,
    loadAddons,
    syncAddons,
    installAddon,
    uninstallAddon,
    updateAddon,
    checkForUpdates,
    cancelDownload,
    getAddon,
    getDownloadProgress,
    addError,
    dismissError,
    clearErrors,
    setSearchQuery,
    setCategory,
    toggleInstalledOnly,
    setSorting,
    cleanup
  };
});
