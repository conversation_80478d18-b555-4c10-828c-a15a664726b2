/**
 * Addon Registry
 * Local SQLite database for tracking installed addons and metadata
 */

import Database from 'better-sqlite3';
import * as path from 'path';
import { Addon, AddonInstallation, AddonUpdate } from '../../types/addon';

export class AddonRegistry {
  private db: Database.Database;

  constructor(dbPath: string) {
    this.db = new Database(dbPath);
    this.initializeDatabase();
  }

  /**
   * Initialize database schema
   */
  private initializeDatabase(): void {
    // Create addons table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS addons (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        links TEXT, -- JSON array
        link_type TEXT,
        version TEXT,
        author TEXT,
        last_updated INTEGER,
        is_installed INTEGER DEFAULT 0,
        installed_version TEXT,
        install_path TEXT,
        tags TEXT, -- JSON array
        featured INTEGER DEFAULT 0,
        download_count INTEGER DEFAULT 0,
        rating REAL,
        screenshots TEXT, -- JSON array
        dependencies TEXT, -- JSON array
        conflicts TEXT, -- JSON array
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);

    // Create installations table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS installations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        addon_id TEXT NOT NULL,
        version TEXT NOT NULL,
        install_date INTEGER NOT NULL,
        install_path TEXT NOT NULL,
        files TEXT NOT NULL, -- JSON array
        backup_path TEXT,
        checksum TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (addon_id) REFERENCES addons (id)
      )
    `);

    // Create updates table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS updates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        addon_id TEXT NOT NULL,
        current_version TEXT NOT NULL,
        available_version TEXT NOT NULL,
        update_url TEXT NOT NULL,
        changelog TEXT,
        release_date INTEGER,
        is_breaking INTEGER DEFAULT 0,
        checked_at INTEGER DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (addon_id) REFERENCES addons (id)
      )
    `);

    // Create indexes
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_addons_name ON addons (name);
      CREATE INDEX IF NOT EXISTS idx_addons_category ON addons (category);
      CREATE INDEX IF NOT EXISTS idx_addons_installed ON addons (is_installed);
      CREATE INDEX IF NOT EXISTS idx_installations_addon_id ON installations (addon_id);
      CREATE INDEX IF NOT EXISTS idx_updates_addon_id ON updates (addon_id);
    `);
  }

  /**
   * Save or update addon
   */
  saveAddon(addon: Addon): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO addons (
        id, name, description, category, links, link_type, version, author,
        last_updated, is_installed, installed_version, install_path, tags,
        featured, download_count, rating, screenshots, dependencies, conflicts,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, strftime('%s', 'now'))
    `);

    stmt.run(
      addon.id,
      addon.name,
      addon.description,
      addon.category,
      JSON.stringify(addon.links),
      addon.linkType,
      addon.version,
      addon.author,
      addon.lastUpdated ? Math.floor(addon.lastUpdated.getTime() / 1000) : null,
      addon.isInstalled ? 1 : 0,
      addon.installedVersion,
      addon.installPath,
      JSON.stringify(addon.tags),
      addon.featured ? 1 : 0,
      addon.downloadCount,
      addon.rating,
      JSON.stringify(addon.screenshots || []),
      JSON.stringify(addon.dependencies || []),
      JSON.stringify(addon.conflicts || [])
    );
  }

  /**
   * Save multiple addons in a transaction
   */
  saveAddons(addons: Addon[]): void {
    const transaction = this.db.transaction((addons: Addon[]) => {
      for (const addon of addons) {
        this.saveAddon(addon);
      }
    });

    transaction(addons);
  }

  /**
   * Get addon by ID
   */
  getAddon(id: string): Addon | null {
    const stmt = this.db.prepare('SELECT * FROM addons WHERE id = ?');
    const row = stmt.get(id) as any;
    
    return row ? this.rowToAddon(row) : null;
  }

  /**
   * Get all addons
   */
  getAllAddons(): Addon[] {
    const stmt = this.db.prepare('SELECT * FROM addons ORDER BY name');
    const rows = stmt.all() as any[];
    
    return rows.map(row => this.rowToAddon(row));
  }

  /**
   * Search addons
   */
  searchAddons(query: string, category?: string, installed?: boolean): Addon[] {
    let sql = `
      SELECT * FROM addons 
      WHERE (name LIKE ? OR description LIKE ?)
    `;
    const params: any[] = [`%${query}%`, `%${query}%`];

    if (category) {
      sql += ' AND category = ?';
      params.push(category);
    }

    if (installed !== undefined) {
      sql += ' AND is_installed = ?';
      params.push(installed ? 1 : 0);
    }

    sql += ' ORDER BY featured DESC, name';

    const stmt = this.db.prepare(sql);
    const rows = stmt.all(...params) as any[];
    
    return rows.map(row => this.rowToAddon(row));
  }

  /**
   * Get installed addons
   */
  getInstalledAddons(): Addon[] {
    const stmt = this.db.prepare('SELECT * FROM addons WHERE is_installed = 1 ORDER BY name');
    const rows = stmt.all() as any[];
    
    return rows.map(row => this.rowToAddon(row));
  }

  /**
   * Save installation record
   */
  saveInstallation(installation: AddonInstallation): number {
    const stmt = this.db.prepare(`
      INSERT INTO installations (
        addon_id, version, install_date, install_path, files, backup_path, checksum
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      installation.addonId,
      installation.version,
      Math.floor(installation.installDate.getTime() / 1000),
      installation.installPath,
      JSON.stringify(installation.files),
      installation.backupPath,
      installation.checksum
    );

    // Update addon as installed
    this.markAddonInstalled(installation.addonId, installation.version, installation.installPath);

    return result.lastInsertRowid as number;
  }

  /**
   * Get installation by addon ID
   */
  getInstallation(addonId: string): AddonInstallation | null {
    const stmt = this.db.prepare(`
      SELECT * FROM installations 
      WHERE addon_id = ? 
      ORDER BY install_date DESC 
      LIMIT 1
    `);
    const row = stmt.get(addonId) as any;
    
    return row ? this.rowToInstallation(row) : null;
  }

  /**
   * Remove installation
   */
  removeInstallation(addonId: string): void {
    const stmt = this.db.prepare('DELETE FROM installations WHERE addon_id = ?');
    stmt.run(addonId);

    // Update addon as not installed
    this.markAddonUninstalled(addonId);
  }

  /**
   * Save update information
   */
  saveUpdate(update: AddonUpdate): void {
    // Remove existing update for this addon
    this.db.prepare('DELETE FROM updates WHERE addon_id = ?').run(update.addonId);

    // Insert new update
    const stmt = this.db.prepare(`
      INSERT INTO updates (
        addon_id, current_version, available_version, update_url, 
        changelog, release_date, is_breaking
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      update.addonId,
      update.currentVersion,
      update.availableVersion,
      update.updateUrl,
      update.changelog,
      update.releaseDate ? Math.floor(update.releaseDate.getTime() / 1000) : null,
      update.isBreaking ? 1 : 0
    );
  }

  /**
   * Get available updates
   */
  getAvailableUpdates(): AddonUpdate[] {
    const stmt = this.db.prepare('SELECT * FROM updates ORDER BY checked_at DESC');
    const rows = stmt.all() as any[];
    
    return rows.map(row => this.rowToUpdate(row));
  }

  /**
   * Mark addon as installed
   */
  private markAddonInstalled(addonId: string, version: string, installPath: string): void {
    const stmt = this.db.prepare(`
      UPDATE addons 
      SET is_installed = 1, installed_version = ?, install_path = ?, updated_at = strftime('%s', 'now')
      WHERE id = ?
    `);
    stmt.run(version, installPath, addonId);
  }

  /**
   * Mark addon as uninstalled
   */
  private markAddonUninstalled(addonId: string): void {
    const stmt = this.db.prepare(`
      UPDATE addons 
      SET is_installed = 0, installed_version = NULL, install_path = NULL, updated_at = strftime('%s', 'now')
      WHERE id = ?
    `);
    stmt.run(addonId);
  }

  /**
   * Convert database row to Addon object
   */
  private rowToAddon(row: any): Addon {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      category: row.category,
      links: JSON.parse(row.links || '[]'),
      linkType: row.link_type,
      version: row.version,
      author: row.author,
      lastUpdated: row.last_updated ? new Date(row.last_updated * 1000) : undefined,
      isInstalled: row.is_installed === 1,
      installedVersion: row.installed_version,
      installPath: row.install_path,
      tags: JSON.parse(row.tags || '[]'),
      featured: row.featured === 1,
      downloadCount: row.download_count,
      rating: row.rating,
      screenshots: JSON.parse(row.screenshots || '[]'),
      dependencies: JSON.parse(row.dependencies || '[]'),
      conflicts: JSON.parse(row.conflicts || '[]')
    };
  }

  /**
   * Convert database row to AddonInstallation object
   */
  private rowToInstallation(row: any): AddonInstallation {
    return {
      addonId: row.addon_id,
      version: row.version,
      installDate: new Date(row.install_date * 1000),
      installPath: row.install_path,
      files: JSON.parse(row.files),
      backupPath: row.backup_path,
      checksum: row.checksum
    };
  }

  /**
   * Convert database row to AddonUpdate object
   */
  private rowToUpdate(row: any): AddonUpdate {
    return {
      addonId: row.addon_id,
      currentVersion: row.current_version,
      availableVersion: row.available_version,
      updateUrl: row.update_url,
      changelog: row.changelog,
      releaseDate: row.release_date ? new Date(row.release_date * 1000) : undefined,
      isBreaking: row.is_breaking === 1
    };
  }

  /**
   * Close database connection
   */
  close(): void {
    this.db.close();
  }
}
