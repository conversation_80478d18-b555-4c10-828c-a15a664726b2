import { ipcRenderer, contextBridge, shell } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
  // IPC methods
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
  send: (channel: string, ...args: any[]) => ipcRenderer.send(channel, ...args),
  on: (channel: string, callback: (...args: any[]) => void) => {
    const subscription = (_event: any, ...args: any[]) => callback(...args)
    ipcRenderer.on(channel, subscription)
    return () => ipcRenderer.removeListener(channel, subscription)
  },

  // Shell methods
  openExternal: (url: string) => shell.openExternal(url),

  // Directory selection
  selectDirectory: () => ipcRenderer.invoke('select-directory'),

  // File system operations (will be handled by main process)
  validateWoWDirectory: (path: string) => ipcRenderer.invoke('validate-wow-directory', path),

  // Addon operations (will be handled by main process)
  scrapeAddons: () => ipcRenderer.invoke('scrape-addons'),
  downloadAddon: (addon: any) => ipcRenderer.invoke('download-addon', addon),
  installAddon: (addonId: string, extractedPath: string) => ipcRenderer.invoke('install-addon', addonId, extractedPath),

  // Database operations
  initDatabase: (dbPath: string) => ipcRenderer.invoke('init-database', dbPath),
  saveAddon: (addon: any) => ipcRenderer.invoke('save-addon', addon),
  getAddons: () => ipcRenderer.invoke('get-addons'),
  searchAddons: (query: string, category?: string, installed?: boolean) => ipcRenderer.invoke('search-addons', query, category, installed),
})

// Legacy support for existing code
contextBridge.exposeInMainWorld('require', (module: string) => {
  // Only allow specific modules that are safe
  const allowedModules = ['electron']
  if (allowedModules.includes(module)) {
    return { ipcRenderer, shell }
  }
  throw new Error(`Module ${module} is not allowed`)
})
