<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
      <!-- Header -->
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-turtle-600 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Welcome to TurtleWoW Addon Manager</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400">Let's get you set up!</p>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div v-if="step === 1">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Your WoW Directory</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Please select your Turtle WoW installation directory. This should contain:
            <br>• A WoW executable (WoW.exe, TurtleWoW.exe, etc.)
            <br>• An "Interface" folder
            <br>• Usually also has "Data" and "WTF" folders
          </p>
          
          <div class="space-y-4">
            <div v-if="selectedDirectory" class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p class="text-sm font-medium text-gray-900 dark:text-white">Selected Directory:</p>
              <p class="text-sm text-gray-600 dark:text-gray-400 break-all">{{ selectedDirectory.path }}</p>
              <div class="mt-2 flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-sm text-green-600 dark:text-green-400">Valid WoW directory detected</span>
              </div>
              <div v-if="selectedDirectory.foundExecutable" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Found: {{ selectedDirectory.foundExecutable }}
              </div>
            </div>

            <button
              @click="selectDirectory"
              :disabled="isSelecting"
              class="w-full btn-primary"
            >
              {{ isSelecting ? 'Selecting...' : 'Browse for WoW Directory' }}
            </button>

            <!-- Debug button -->
            <button
              @click="testElectronAPI"
              class="w-full btn-secondary text-sm"
            >
              Test Electron API (Debug)
            </button>

            <div v-if="error" class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
              <details class="mt-2">
                <summary class="text-xs text-red-500 cursor-pointer">Show debug info</summary>
                <div class="mt-2 text-xs text-red-500 font-mono">
                  <p>Check the console (F12) for more details</p>
                  <p>Looking for files like: WoW.exe, TurtleWoW.exe, Interface folder</p>
                </div>
              </details>
            </div>
          </div>
        </div>

        <div v-else-if="step === 2">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Initial Setup</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Great! Now let's download the latest addon list from the Turtle WoW wiki.
          </p>

          <div class="space-y-4">
            <div v-if="isSyncing" class="text-center">
              <svg class="animate-spin w-8 h-8 text-primary-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
              </svg>
              <p class="text-sm text-gray-600 dark:text-gray-400">Downloading addon list...</p>
            </div>

            <div v-else-if="syncComplete" class="text-center">
              <svg class="w-8 h-8 text-green-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <p class="text-sm text-gray-600 dark:text-gray-400">Successfully downloaded {{ addonCount }} addons!</p>
            </div>

            <div v-else>
              <button
                @click="syncAddons"
                class="w-full btn-primary"
              >
                Download Addon List
              </button>
            </div>

            <div v-if="syncError" class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p class="text-sm text-red-600 dark:text-red-400">{{ syncError }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
        <button
          v-if="step > 1"
          @click="step--"
          class="btn-secondary"
        >
          Back
        </button>
        <div v-else></div>

        <button
          v-if="step === 1"
          @click="step++"
          :disabled="!selectedDirectory"
          class="btn-primary"
        >
          Next
        </button>
        <button
          v-else-if="step === 2 && syncComplete"
          @click="completeSetup"
          class="btn-success"
        >
          Get Started!
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useSettingsStore } from '../stores/settingsStore'
import { useAddonStore } from '../stores/addonStore'
import { WoWDirectory } from '../types/addon'

const settingsStore = useSettingsStore()
const addonStore = useAddonStore()

const step = ref(1)
const selectedDirectory = ref<WoWDirectory | null>(null)
const isSelecting = ref(false)
const error = ref('')
const isSyncing = ref(false)
const syncComplete = ref(false)
const syncError = ref('')
const addonCount = ref(0)

async function selectDirectory() {
  try {
    isSelecting.value = true
    error.value = ''

    // Check if electronAPI is available
    if (!window.electronAPI) {
      throw new Error('Electron API not available. Are you running in Electron?')
    }

    // Use Electron's dialog to select directory
    console.log('📁 Calling selectDirectory...')
    const result = await window.electronAPI.selectDirectory()
    console.log('📁 Directory selection result:', result)

    if (result.canceled || !result.filePaths.length) {
      console.log('📁 Directory selection was canceled or no paths selected')
      return
    }

    const selectedPath = result.filePaths[0]
    console.log('🔍 Selected path:', selectedPath)
    console.log('🔍 Type of selected path:', typeof selectedPath)

    // Validate the directory
    const validation = await window.electronAPI.validateWoWDirectory(selectedPath)
    console.log('🔍 Validation result:', validation)

    if (validation && validation.isValid) {
      selectedDirectory.value = validation
      // Save to settings store
      settingsStore.setWoWDirectory(validation)
      error.value = ''
    } else {
      error.value = validation?.error || 'Please select a valid Turtle WoW installation directory'
      selectedDirectory.value = null
    }
  } catch (err) {
    console.error('❌ Directory selection failed:', err)
    error.value = err instanceof Error ? err.message : 'Failed to select directory'
    selectedDirectory.value = null
  } finally {
    isSelecting.value = false
  }
}

async function syncAddons() {
  try {
    isSyncing.value = true
    syncError.value = ''
    
    // Initialize addon store
    await addonStore.initialize(settingsStore.settings)
    
    // Sync addons
    const count = await addonStore.syncAddons()
    addonCount.value = count
    syncComplete.value = true
  } catch (err) {
    syncError.value = err instanceof Error ? err.message : 'Failed to download addon list'
  } finally {
    isSyncing.value = false
  }
}

function completeSetup() {
  settingsStore.saveSettings()
}

function testElectronAPI() {
  console.log('🧪 Testing Electron API...')
  console.log('🧪 window.electronAPI exists:', !!window.electronAPI)
  console.log('🧪 window.electronAPI:', window.electronAPI)

  if (window.electronAPI) {
    console.log('🧪 Available methods:', Object.keys(window.electronAPI))

    // Test a simple invoke
    window.electronAPI.invoke('get-settings').then(result => {
      console.log('🧪 get-settings result:', result)
    }).catch(error => {
      console.error('🧪 get-settings error:', error)
    })
  } else {
    console.error('🧪 Electron API not available!')
  }
}
</script>
