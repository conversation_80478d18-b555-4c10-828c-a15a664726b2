<template>
  <div class="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-turtle-600 rounded-lg flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-lg font-semibold text-gray-900 dark:text-white">TurtleWoW</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400">Addon Manager</p>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 p-4 space-y-2">
      <router-link
        to="/"
        class="nav-item"
        :class="{ 'nav-item-active': $route.name === 'browser' }"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <span>Browse Addons</span>
      </router-link>

      <router-link
        to="/updates"
        class="nav-item"
        :class="{ 'nav-item-active': $route.name === 'updates' }"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        <span>Updates</span>
        <span v-if="addonStore.availableUpdates > 0" class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          {{ addonStore.availableUpdates }}
        </span>
      </router-link>

      <div class="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
        <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3 mb-2">
          Installed
        </p>
        <div class="space-y-1">
          <div class="flex items-center justify-between px-3 py-2 text-sm text-gray-600 dark:text-gray-300">
            <span>{{ addonStore.installedAddons.length }} addons</span>
            <button
              @click="addonStore.checkForUpdates()"
              class="text-primary-600 hover:text-primary-700 dark:text-primary-400"
              title="Check for updates"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Footer -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
      <!-- Sync Button -->
      <button
        @click="addonStore.syncAddons()"
        :disabled="addonStore.isSyncing"
        class="w-full btn-primary flex items-center justify-center space-x-2"
      >
        <svg 
          class="w-4 h-4" 
          :class="{ 'animate-spin': addonStore.isSyncing }"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        <span>{{ addonStore.isSyncing ? 'Syncing...' : 'Sync Addons' }}</span>
      </button>

      <!-- Settings Link -->
      <router-link
        to="/settings"
        class="nav-item"
        :class="{ 'nav-item-active': $route.name === 'settings' }"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
        </svg>
        <span>Settings</span>
      </router-link>

      <!-- Last Sync Info -->
      <div v-if="addonStore.lastSync" class="text-xs text-gray-500 dark:text-gray-400 px-3">
        Last sync: {{ formatDate(addonStore.lastSync) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAddonStore } from '../stores/addonStore'

const addonStore = useAddonStore()

function formatDate(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return 'just now'
  if (minutes < 60) return `${minutes}m ago`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}
</script>

<style scoped>
.nav-item {
  @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100;
  @apply dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700;
}

.nav-item-active {
  @apply bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400;
}
</style>
