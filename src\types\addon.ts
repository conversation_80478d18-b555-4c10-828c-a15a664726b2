/**
 * Core data models for TurtleWoW Addon Manager
 */

export interface Addon {
  id: string;
  name: string;
  description: string;
  category: AddonCategory;
  links: AddonLink[];
  linkType: LinkType;
  version?: string;
  author?: string;
  lastUpdated?: Date;
  isInstalled: boolean;
  installedVersion?: string;
  installPath?: string;
  tags: string[];
  featured: boolean;
  downloadCount?: number;
  rating?: number;
  screenshots?: string[];
  dependencies?: string[];
  conflicts?: string[];
}

export interface AddonLink {
  url: string;
  type: LinkSourceType;
  label?: string;
  isPrimary: boolean;
}

export enum LinkType {
  DIRECT_DOWNLOAD = 'direct_download',
  EXTERNAL_ONLY = 'external_only',
  MIXED = 'mixed'
}

export enum LinkSourceType {
  GITHUB_REPO = 'github_repo',
  GITHUB_RELEASE = 'github_release',
  GITLAB_REPO = 'gitlab_repo',
  GITLAB_RELEASE = 'gitlab_release',
  DIRECT_ZIP = 'direct_zip',
  CURSEFORGE = 'curseforge',
  FORUM_POST = 'forum_post',
  CUSTOM_HOST = 'custom_host',
  UNKNOWN = 'unknown'
}

export enum AddonCategory {
  FEATURED = 'featured',
  UI_REPLACEMENT = 'ui_replacement',
  LEVELING = 'leveling',
  ENDGAME = 'endgame',
  COMBAT = 'combat',
  UTILITY = 'utility',
  ROLEPLAY = 'roleplay',
  PROFESSION = 'profession',
  PVP = 'pvp',
  LIBRARY = 'library',
  OTHER = 'other'
}

export interface AddonInstallation {
  addonId: string;
  version: string;
  installDate: Date;
  installPath: string;
  files: string[];
  backupPath?: string;
  checksum?: string;
}

export interface AddonUpdate {
  addonId: string;
  currentVersion: string;
  availableVersion: string;
  updateUrl: string;
  changelog?: string;
  releaseDate?: Date;
  isBreaking: boolean;
}

export interface DownloadProgress {
  addonId: string;
  status: DownloadStatus;
  progress: number; // 0-100
  downloadedBytes: number;
  totalBytes: number;
  speed: number; // bytes per second
  eta: number; // seconds remaining
  error?: string;
}

export enum DownloadStatus {
  PENDING = 'pending',
  DOWNLOADING = 'downloading',
  EXTRACTING = 'extracting',
  INSTALLING = 'installing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface ScrapedAddonData {
  name: string;
  description: string;
  url: string;
  section: string; // A, B, C, etc.
  rawHtml: string;
  scrapedAt: Date;
}

export interface WoWDirectory {
  path: string;
  isValid: boolean;
  addonsPath: string;
  version?: string;
  lastChecked: Date;
}

export interface AppSettings {
  wowDirectory: WoWDirectory | null;
  autoUpdate: boolean;
  downloadConcurrency: number;
  backupBeforeUpdate: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    updates: boolean;
    downloads: boolean;
    errors: boolean;
  };
  advanced: {
    validateChecksums: boolean;
    retryAttempts: number;
    timeoutSeconds: number;
  };
}

export interface AppState {
  addons: Addon[];
  installations: AddonInstallation[];
  downloads: DownloadProgress[];
  updates: AddonUpdate[];
  settings: AppSettings;
  isLoading: boolean;
  lastSync: Date | null;
  errors: AppError[];
}

export interface AppError {
  id: string;
  type: ErrorType;
  message: string;
  details?: string;
  timestamp: Date;
  addonId?: string;
  dismissed: boolean;
}

export enum ErrorType {
  NETWORK = 'network',
  FILESYSTEM = 'filesystem',
  VALIDATION = 'validation',
  PARSING = 'parsing',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown'
}

// API Response types
export interface GitHubRelease {
  tag_name: string;
  name: string;
  body: string;
  published_at: string;
  assets: GitHubAsset[];
  prerelease: boolean;
  draft: boolean;
}

export interface GitHubAsset {
  name: string;
  browser_download_url: string;
  size: number;
  content_type: string;
  download_count: number;
}

export interface GitLabRelease {
  tag_name: string;
  name: string;
  description: string;
  created_at: string;
  assets: {
    links: GitLabAssetLink[];
  };
}

export interface GitLabAssetLink {
  name: string;
  url: string;
  link_type: string;
}
