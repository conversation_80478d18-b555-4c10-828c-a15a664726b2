import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHashHistory } from 'vue-router'
import './style.css'
import App from './App.vue'

// Import views
import AddonBrowser from './views/AddonBrowser.vue'
import AddonDetails from './views/AddonDetails.vue'
import Settings from './views/Settings.vue'
import Updates from './views/Updates.vue'
import DirectoryTest from './views/DirectoryTest.vue'

// Create router
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'browser',
      component: AddonBrowser
    },
    {
      path: '/addon/:id',
      name: 'addon-details',
      component: AddonDetails,
      props: true
    },
    {
      path: '/updates',
      name: 'updates',
      component: Updates
    },
    {
      path: '/settings',
      name: 'settings',
      component: Settings
    },
    {
      path: '/test',
      name: 'directory-test',
      component: DirectoryTest
    }
  ]
})

// Create Pinia store
const pinia = createPinia()

// Create and mount app
const app = createApp(App)
app.use(pinia)
app.use(router)
app.mount('#app')
