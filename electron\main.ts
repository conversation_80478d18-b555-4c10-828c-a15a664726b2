import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import Store from 'electron-store'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

// Initialize electron-store
const store = new Store()

function createWindow() {
  win = new BrowserWindow({
    title: 'TurtleWoW Addon Manager',
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) require('electron').shell.openExternal(url)
    return { action: 'deny' }
  })
}

// IPC handlers
ipcMain.handle('select-directory', async () => {
  if (!win) return { canceled: true }

  console.log('📁 Opening directory selection dialog...')

  const result = await dialog.showOpenDialog(win, {
    properties: ['openDirectory'],
    title: 'Select Turtle WoW Directory',
    message: 'Please select your Turtle WoW installation directory'
  })

  console.log('📁 Dialog result:', result)

  return result
})

// Settings handlers
ipcMain.handle('get-settings', async () => {
  try {
    return store.get('settings', {})
  } catch (error) {
    console.error('Failed to get settings:', error)
    return {}
  }
})

ipcMain.handle('save-settings', async (_event, settings) => {
  try {
    store.set('settings', settings)
    console.log('✅ Settings saved successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to save settings:', error)
    return false
  }
})

ipcMain.handle('get-first-run', async () => {
  try {
    return store.get('firstRun', true)
  } catch (error) {
    console.error('Failed to get first run status:', error)
    return true
  }
})

ipcMain.handle('save-first-run', async (_event, firstRun) => {
  try {
    store.set('firstRun', firstRun)
    console.log('✅ First run status saved:', firstRun)
    return true
  } catch (error) {
    console.error('❌ Failed to save first run status:', error)
    return false
  }
})

// WoW directory validation
ipcMain.handle('validate-wow-directory', async (_event, dirPath) => {
  const fs = require('fs')
  const path = require('path')

  try {
    console.log('🔍 Validating WoW directory:', dirPath)
    console.log('🔍 Type of dirPath:', typeof dirPath)

    // Check if directory exists
    if (!fs.existsSync(dirPath)) {
      console.log('❌ Directory does not exist')
      return { isValid: false, error: 'Directory does not exist' }
    }

    // Look for WoW executable (more flexible for Turtle WoW)
    const possibleExes = [
      'WoW.exe', 'Wow.exe', 'wow.exe',           // Standard WoW
      'TurtleWoW.exe', 'turtle-wow.exe',         // Turtle WoW specific
      'World of Warcraft.exe',                   // Some distributions
      'Wow-64.exe', 'WowClassic.exe'             // Other variants
    ]

    const files = fs.readdirSync(dirPath)
    console.log('📁 Files in directory:', files.slice(0, 10)) // Log first 10 files

    const foundExe = possibleExes.find(exe =>
      fs.existsSync(path.join(dirPath, exe))
    )

    // Also check for any .exe file that might be WoW-related
    const exeFiles = files.filter(file => file.toLowerCase().endsWith('.exe'))
    console.log('🎮 Found .exe files:', exeFiles)

    if (!foundExe && exeFiles.length === 0) {
      console.log('❌ No WoW executable found')
      return {
        isValid: false,
        error: 'No WoW executable found. Looking for: ' + possibleExes.join(', '),
        foundFiles: exeFiles
      }
    }

    // Check for Interface directory (more important than exe)
    const interfacePath = path.join(dirPath, 'Interface')
    if (!fs.existsSync(interfacePath)) {
      console.log('❌ Interface directory not found')
      return {
        isValid: false,
        error: 'Interface directory not found. This doesn\'t appear to be a WoW installation.'
      }
    }

    const addonsPath = path.join(interfacePath, 'AddOns')

    // Create AddOns directory if it doesn't exist
    if (!fs.existsSync(addonsPath)) {
      console.log('📁 Creating AddOns directory')
      fs.mkdirSync(addonsPath, { recursive: true })
    }

    console.log('✅ Valid WoW directory found')
    return {
      path: dirPath,
      isValid: true,
      addonsPath,
      lastChecked: new Date(),
      foundExecutable: foundExe || exeFiles[0] || 'Unknown'
    }

  } catch (error) {
    console.error('❌ Error validating WoW directory:', error)
    return {
      isValid: false,
      error: `Validation failed: ${error.message}`
    }
  }
})

// Placeholder handlers for addon operations
ipcMain.handle('scrape-addons', async () => {
  // For now, return empty array - will implement later
  console.log('Scraping addons...')
  return []
})

ipcMain.handle('get-addons', async () => {
  // For now, return empty array - will implement later
  return []
})

ipcMain.handle('search-addons', async (_event, _query, _category, _installed) => {
  // For now, return empty array - will implement later
  return []
})

ipcMain.handle('install-addon-full', async (_event, addonId, _createBackup) => {
  // For now, return success - will implement later
  console.log('Installing addon:', addonId)
  return { success: true }
})

ipcMain.handle('get-user-data-path', async () => {
  return require('electron').app.getPath('userData')
})

// Database handlers
ipcMain.handle('init-database', async (_event, dbPath) => {
  console.log('💾 Initializing database at:', dbPath)
  // For now, just return success - will implement later
  return true
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(createWindow)
