<template>
  <div class="card p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer" @click="$emit('viewDetails', addon.id)">
    <div class="flex items-center justify-between">
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
          {{ addon.name }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
          {{ addon.description }}
        </p>
      </div>
      <div class="ml-4 flex items-center space-x-2">
        <span v-if="addon.isInstalled" class="text-green-600 dark:text-green-400 text-sm">Installed</span>
        <button
          v-if="addon.linkType === 'direct_download'"
          @click.stop="addon.isInstalled ? $emit('uninstall', addon.id) : $emit('install', addon.id)"
          :class="addon.isInstalled ? 'btn-danger' : 'btn-success'"
          class="text-sm px-3 py-1"
        >
          {{ addon.isInstalled ? 'Uninstall' : 'Install' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Addon } from '../types/addon'

interface Props {
  addon: Addon
}

defineProps<Props>()

defineEmits<{
  install: [addonId: string]
  uninstall: [addonId: string]
  viewDetails: [addonId: string]
}>()
</script>
